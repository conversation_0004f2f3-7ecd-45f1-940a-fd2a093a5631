{% extends "base/harmattan.html" %}

{% block title %}SQL Client - Harmattan{% endblock %}

{% block header %}
<small class="text-muted">Query your data</small>
<h1 class="h4 mt-1">SQL Client</h1>
{% endblock %}

{% block header_actions %}
<button type="button" class="btn btn-success" id="runQueryBtn">
    <i class="fa fa-play me-1"></i> Run Query
</button>
<button type="button" class="btn btn-outline-secondary" id="clearBtn">
    <i class="fa fa-eraser me-1"></i> Clear
</button>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/monokai.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap5.min.css">
<style>
.sql-editor {
    height: 50vh;
    border: 1px solid #ddd;
    border-radius: 4px;
}
.CodeMirror {
    height: 100%;
    font-size: 14px;
}
/* Custom keyword styling for Harmattan SQL commands */
.cm-custom-keyword {
    color: #d73a49 !important;
    font-weight: bold;
}
.results-panel {
    min-height: 200px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
}
#resultsContent {
    /* Ensure results content expands to fit content without scrolling */
    height: auto !important;
    max-height: none !important;
    overflow: visible !important;
}

/* Override layout.a.min.css styles that cause scrolling */
#layout-a .main {
    height: auto !important;
    overflow-y: visible !important;
}

/* Ensure the body container also expands */
#layout-a .main .body {
    min-height: auto !important;
    height: auto !important;
}

/* Force all parent containers to not have height restrictions */
.container-fluid, .row, .col-12 {
    height: auto !important;
    max-height: none !important;
    overflow: visible !important;
}

/* Specifically target the card and card-body that contain the results */
.card, .card-body {
    height: auto !important;
    max-height: none !important;
    overflow: visible !important;
}
.query-info {
    background: #f8f9fa;
    padding: 8px 12px;
    border-bottom: 1px solid #ddd;
    font-size: 12px;
}

.results-table {
    font-size: 12px;
}
.results-table th {
    background: #f8f9fa;
    position: sticky;
    top: 0;
    z-index: 10;
}
.error-message {
    color: #dc3545;
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    padding: 12px;
    margin: 12px;
}

/* Custom SQL keyword highlighting */
.CodeMirror .cm-keyword.cm-custom-keyword {
    color: #e91e63 !important;
    font-weight: bold;
    background: rgba(233, 30, 99, 0.1);
    border-radius: 2px;
    padding: 1px 2px;
}

/* Visualization results styling */
.visualization-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    margin: 10px;
}

/* Table summary styling for TABULATE results - using same style as SELECT results */

.table-summary-container .gt_table,
.table-summary-container table {
    /* Apply same styling as results-table */
    font-size: 12px !important;
    width: 100%;
    border-collapse: collapse;
}

.table-summary-container .gt_col_headings th,
.table-summary-container .gt_col_heading,
.table-summary-container thead th,
.table-summary-container th {
    background: #f8f9fa !important;
    font-weight: bold;
    position: sticky;
    top: 0;
    z-index: 10;
    padding: 8px 12px !important;
    border-bottom: 2px solid #dee2e6 !important;
    font-size: 12px !important;
}

.table-summary-container .gt_row,
.table-summary-container tbody tr {
    border-bottom: 1px solid #dee2e6;
}

.table-summary-container .gt_row:nth-child(even),
.table-summary-container tbody tr:nth-child(even) {
    background-color: rgba(0,0,0,.05) !important;
}

.table-summary-container .gt_row:hover,
.table-summary-container tbody tr:hover {
    background-color: #f5f5f5 !important;
}

.table-summary-container td,
.table-summary-container .gt_row td,
.table-summary-container tbody td {
    padding: 8px 12px !important;
    font-size: 12px !important;
    border-top: none !important;
}

/* Override any gtsummary default styling */
.table-summary-container .gt_table .gt_heading {
    background: transparent !important;
    border: none !important;
    padding: 10px !important;
    text-align: center;
    font-weight: bold;
}

.table-summary-container .gt_table .gt_footnotes {
    font-size: 11px !important;
    padding: 8px !important;
}

/* Ensure single variable visualizations use 3 columns */
.visualization-container.single-variable {
    grid-template-columns: repeat(3, 1fr);
    min-height: 400px;
}

/* When there are 4 plots (typical single variable), arrange nicely */
.visualization-container.single-variable .visualization-item:nth-child(4) {
    grid-column: 2; /* Center the 4th plot in the second row */
}

.visualization-item {
    background: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
}

.visualization-item h6 {
    margin-bottom: 15px;
    color: #495057;
    font-weight: 600;
}

.visualization-item svg, .visualization-item img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
}

.image-container, .svg-container {
    cursor: pointer;
    transition: transform 0.2s ease;
}

.image-container:hover, .svg-container:hover {
    transform: scale(1.02);
}

/* Responsive design for visualizations */
@media (max-width: 1200px) {
    .visualization-container.single-variable {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .visualization-container {
        grid-template-columns: 1fr;
    }

    .visualization-container.single-variable {
        grid-template-columns: 1fr;
    }
}

/* Multi-column visualization styling */
.plot-type-section {
    margin-bottom: 30px;
}

.plot-type-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 15px;
    padding: 10px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
    text-align: center;
    margin: 10px;
}

/* Adjust grid for multi-column when there are many plots */
.plot-type-section .visualization-container {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
}

@media (max-width: 768px) {
    .plot-type-section .visualization-container {
        grid-template-columns: 1fr;
    }
}

/* Bivariate visualization styling */
.visualization-container.bivariate-single {
    grid-template-columns: 1fr;
    justify-items: center;
    max-width: 800px;
    margin: 10px auto;
}

.visualization-container.bivariate-barchart {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

@media (max-width: 768px) {
    .visualization-container.bivariate-barchart {
        grid-template-columns: 1fr;
    }
}
.loading {
    text-align: center;
    padding: 40px;
    color: #6c757d;
}

/* SQL Editor Collapsible Styling */
.sql-editor-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.sql-editor-header:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.sql-editor-header.collapsed {
    background: #f8f9fa;
    color: #495057;
    border-bottom: 1px solid #dee2e6;
}

.sql-editor-header.collapsed:hover {
    background: #e9ecef;
}

#toggleEditorBtn {
    border: none !important;
    text-decoration: none !important;
    color: inherit !important;
    transition: transform 0.3s ease;
}

#toggleEditorBtn:hover {
    color: inherit !important;
    text-decoration: none !important;
}

#toggleIcon {
    transition: transform 0.3s ease;
}

#toggleIcon.rotated {
    transform: rotate(-90deg);
}

.sql-editor-content {
    display: block;
    transition: all 0.3s ease;
}

.sql-editor-content.collapsed {
    display: none !important;
}

/* Results panel enhancement when editor is collapsed */
.results-panel.expanded {
    min-height: 400px;
    transition: min-height 0.3s ease;
}

/* DataTables styling */
.dataTables_wrapper {
    padding: 0;
}

/* Horizontal scrolling for DataTables */
.dataTables_scrollHead,
.dataTables_scrollBody {
    overflow-x: auto !important;
}

.dataTables_scroll {
    overflow: auto;
}

/* Ensure table maintains proper styling with horizontal scroll */
.dataTables_scrollBody table {
    margin: 0 !important;
}

/* Style the horizontal scrollbar */
.dataTables_scrollBody::-webkit-scrollbar {
    height: 8px;
}

.dataTables_scrollBody::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.dataTables_scrollBody::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.dataTables_scrollBody::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Prevent table from being too narrow and ensure proper column sizing */
#paginatedTable {
    min-width: 100%;
    white-space: nowrap;
}

/* Ensure table cells don't wrap text when scrolling horizontally */
#paginatedTable td,
#paginatedTable th {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px; /* Limit column width for better readability */
}

/* Allow some columns to be wider if needed */
#paginatedTable td:hover,
#paginatedTable th:hover {
    overflow: visible;
    white-space: normal;
    word-wrap: break-word;
}

.dataTables_info {
    font-size: 12px;
    color: #6c757d;
}

.dataTables_paginate {
    font-size: 12px;
}

.dataTables_length select {
    font-size: 12px;
}

.dataTables_filter input {
    font-size: 12px;
}

.pagination-info {
    background: #f8f9fa;
    padding: 8px 12px;
    border-bottom: 1px solid #ddd;
    font-size: 12px;
    display: flex;
    justify-content: between;
    align-items: center;
}

.pagination-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.page-size-selector {
    font-size: 12px;
}

#paginatedTable {
    font-size: 12px;
}

#paginatedTable th {
    background: #f8f9fa;
    position: sticky;
    top: 0;
    z-index: 10;
    font-size: 12px;
}

.table-loading {
    text-align: center;
    padding: 40px;
    color: #6c757d;
}
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- SQL Editor Collapsible -->
        <div class="card mb-3" id="sqlEditorCard">
            <div class="card-header sql-editor-header" id="sqlEditorHeader">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <button type="button" class="btn btn-link p-0 me-3 text-white" id="toggleEditorBtn" title="Click to collapse/expand">
                            <i class="fa fa-chevron-down" id="toggleIcon"></i>
                        </button>
                        <i class="fa fa-code me-2"></i>
                        <span class="fw-bold">SQL Editor</span>
                        <small class="text-muted ms-3">Press Ctrl+Enter to run query</small>
                    </div>
                    <div>
                        <button type="button" class="btn btn-sm btn-outline-light" data-bs-toggle="modal" data-bs-target="#helpModal">
                            <i class="fa fa-question-circle"></i> Help
                        </button>
                    </div>
                </div>
            </div>
            <div id="sqlEditorContent" class="sql-editor-content">
                <div class="d-flex justify-content-between align-items-center p-3 border-bottom bg-light">
                    <div class="d-flex align-items-center">
                        <small class="text-muted me-3">
                            <i class="fa fa-keyboard-o me-1"></i>Ctrl+Enter to run
                        </small>
                        <small class="text-muted me-3">
                            <i class="fa fa-eye-slash me-1"></i>Click header to collapse
                        </small>
                        <small class="text-muted">
                            <i class="fa fa-keyboard-o me-1"></i>Ctrl+Shift+E to toggle
                        </small>
                    </div>
                </div>
                <div class="sql-editor">
                    <textarea id="sqlEditor" placeholder="-- Enter your SQL query here
-- Example: SELECT * FROM your_table_name LIMIT 10;"></textarea>
                </div>
            </div>
        </div>

        <!-- Results Panel -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">Query Results</h6>
            </div>
            <div class="card-body p-0">
                <div class="results-panel">
                    <div id="resultsContent" class="text-center py-5 text-muted">
                        <i class="fa fa-code fa-2x mb-3"></i>
                        <p>Run a SQL query to see results here</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Help Modal -->
<div class="modal fade" id="helpModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">SQL Client Help</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>Available Tables:</h6>
                <div class="mb-3">
                    {% if tables %}
                        {% for table in tables %}
                        <span class="badge bg-secondary me-1 mb-1">{{ table.name }}</span>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">No tables available. Upload data first.</p>
                    {% endif %}
                </div>
                
                <h6>Example Queries:</h6>
                <div class="mb-3">
                    <pre class="bg-light p-2 rounded"><code>-- View all data from a table
SELECT * FROM table_name LIMIT 100;

-- Count rows
SELECT COUNT(*) FROM table_name;

-- Group by example
SELECT column_name, COUNT(*) 
FROM table_name 
GROUP BY column_name;

-- Join tables
SELECT a.*, b.column_name
FROM table1 a
JOIN table2 b ON a.id = b.id;</code></pre>
                </div>

                <h6>Custom Harmattan SQL Commands:</h6>
                <div class="alert alert-info">
                    <strong>VISUALIZE</strong> - Generate statistical visualizations using R and ggplot2
                </div>
                <pre class="bg-light p-2 rounded"><code>-- Create histogram, density, box and violin plots
VISUALIZE numeric_column FROM table_name;

-- Examples:
VISUALIZE price FROM products;
VISUALIZE age FROM customers;</code></pre>

                <p><strong>VISUALIZE</strong> creates four types of plots:</p>
                <ul>
                    <li><strong>Histogram</strong> - Shows frequency distribution</li>
                    <li><strong>Density Plot</strong> - Shows probability density</li>
                    <li><strong>Box Plot</strong> - Shows quartiles and outliers</li>
                    <li><strong>Violin Plot</strong> - Combines density and box plot</li>
                </ul>

                <div class="alert alert-warning">
                    <strong>Requirements:</strong> R with ggplot2 and dplyr packages must be installed for VISUALIZE commands.
                </div>

                <h6>Custom Harmattan SQL Commands:</h6>
                <div class="alert alert-info">
                    <strong>DESCRIBE</strong> - Show table schema and column information<br>
                    <strong>VISUALIZE</strong> - Generate statistical visualizations using R and ggplot2<br>
                    <small>• <strong>Numeric columns:</strong> Histogram, Density, Box Plot, Violin Plot</small><br>
                    <small>• <strong>Categorical columns:</strong> Bar Chart, Pie Chart, Doughnut Chart</small><br>
                    <small>• <strong>Bivariate plots:</strong> Scatterplot, Grouped Boxplot, Grouped/Stacked Bar Charts</small><br>
                    <strong>TABULATE</strong> - Generate summary statistics tables using R and gtsummary<br>
                    <small>• <strong>Numeric variables:</strong> Median (Q1, Q3)</small><br>
                    <small>• <strong>Categorical variables:</strong> Frequency counts and percentages</small><br>
                    <small>• <strong>Bivariate tables:</strong> Statistics by groups with p-values</small><br>
                    <strong>CORR</strong> - Generate correlation heatmaps for numeric variables<br>
                    <small>• <strong>Correlation matrix:</strong> Pearson correlations with diverging color palette</small><br>
                    <small>• <strong>Visual output:</strong> Professional heatmap with correlation values</small><br>
                    <strong>REGRESS</strong> - Perform regression analysis with automatic model selection<br>
                    <small>• <strong>Linear regression:</strong> For continuous outcomes (lm)</small><br>
                    <small>• <strong>Logistic regression:</strong> For binary outcomes (glm)</small><br>
                    <small>• <strong>Smart sampling:</strong> Automatic random sampling up to 10,000 rows</small>
                </div>
                <pre class="bg-light p-2 rounded"><code>-- Show table structure and columns
DESCRIBE table_name;

-- Single column visualization
VISUALIZE numeric_column FROM table_name;

-- Multiple columns visualization
VISUALIZE column1, column2, column3 FROM table_name;

-- With WHERE clause (filtering)
VISUALIZE numeric_column FROM table_name WHERE condition = 'value';

-- With LIMIT clause (limit data points)
VISUALIZE numeric_column FROM table_name LIMIT 100;

-- Combined WHERE and LIMIT
VISUALIZE numeric_column FROM table_name WHERE var1 = 'value' AND var2 < 45 LIMIT 100;

-- Multi-column with filters
VISUALIZE Age, Height, Weight FROM nhanes WHERE Gender = 'Female' LIMIT 500;

-- Examples:
DESCRIBE nhanes;

-- Numeric visualizations (histogram, density, box, violin)
VISUALIZE Age FROM nhanes;
VISUALIZE Age, Height FROM nhanes;

-- Categorical visualizations (bar, pie, doughnut)
VISUALIZE Gender FROM nhanes;
VISUALIZE Race, Education FROM nhanes;

-- Mixed numeric and categorical
VISUALIZE Age, Gender FROM nhanes WHERE Age > 18;

-- Bivariate visualizations
VISUALIZE SCATTERPLOT(Age, Height) FROM nhanes;
VISUALIZE BOXPLOT(Age) FROM nhanes GROUP BY Gender;
VISUALIZE BARCHART(Education) FROM nhanes GROUP BY Gender;

-- Summary statistics tables
TABULATE Age, Height, Weight FROM nhanes;
TABULATE Age, Gender FROM nhanes WHERE Age > 18;
TABULATE Age, Height FROM nhanes GROUP BY Gender;
TABULATE Education, Income FROM nhanes WHERE Age > 25 LIMIT 1000;

-- Correlation analysis
VISUALIZE CORR(Age, Height, Weight) FROM nhanes;
VISUALIZE CORR(Age, BMI) FROM nhanes WHERE Gender = 'Female';
VISUALIZE CORR(Age, Height, Weight, BMI) FROM nhanes LIMIT 500;

-- Regression analysis
REGRESS Age, Height FROM nhanes GROUP BY Weight;
REGRESS Age, Race1, Gender FROM nhanes GROUP BY BMI;
REGRESS Age, BMI FROM nhanes WHERE Race1 = 'White' GROUP BY Gender LIMIT 200;</code></pre>

                <p><strong>VISUALIZE</strong> creates different plots based on data type:</p>

                <p><strong>📊 Numeric Variables:</strong></p>
                <ul>
                    <li><strong>Histogram</strong> - Shows frequency distribution</li>
                    <li><strong>Density Plot</strong> - Shows probability density</li>
                    <li><strong>Box Plot</strong> - Shows quartiles and outliers</li>
                    <li><strong>Violin Plot</strong> - Combines density and box plot</li>
                </ul>

                <p><strong>📈 Categorical Variables:</strong></p>
                <ul>
                    <li><strong>Bar Chart</strong> - Shows frequency count of each category</li>
                    <li><strong>Pie Chart</strong> - Shows proportional distribution</li>
                    <li><strong>Doughnut Chart</strong> - Modern alternative to pie chart</li>
                </ul>

                <p><strong>🔗 Bivariate Visualizations:</strong></p>
                <ul>
                    <li><strong>Scatterplot</strong> - Shows relationship between two numeric variables</li>
                    <li><strong>Grouped Boxplot</strong> - Shows numeric distribution by categorical groups</li>
                    <li><strong>Grouped Bar Chart</strong> - Shows categorical counts by groups</li>
                    <li><strong>Stacked Bar Chart</strong> - Shows categorical percentages by groups</li>
                </ul>

                <p><strong>Advanced VISUALIZE Features:</strong></p>
                <ul>
                    <li><strong>Multiple columns</strong> - Visualize multiple numeric/categorical variables at once</li>
                    <li><strong>Mixed data types</strong> - Combine numeric and categorical columns in one command</li>
                    <li><strong>Bivariate analysis</strong> - Explore relationships between two variables</li>
                    <li><strong>WHERE clause</strong> - Filter data before visualization (e.g., specific gender, age range)</li>
                    <li><strong>LIMIT clause</strong> - Limit number of data points for performance</li>
                    <li><strong>Combined filters</strong> - Use both WHERE and LIMIT together</li>
                    <li><strong>Case-insensitive</strong> - Column names work regardless of case</li>
                    <li><strong>Organized layout</strong> - Multi-column results grouped by plot type</li>
                    <li><strong>Auto-detection</strong> - Automatically detects numeric vs categorical columns</li>
                </ul>

                <p><strong>📋 TABULATE Command:</strong></p>
                <p>The <strong>TABULATE</strong> command generates professional summary statistics tables using the gtsummary R package.</p>

                <p><strong>📊 Numeric Variables:</strong></p>
                <ul>
                    <li><strong>Median (Q1, Q3)</strong> - Shows median with first and third quartiles</li>
                    <li><strong>Robust statistics</strong> - Less sensitive to outliers than mean/SD</li>
                </ul>

                <p><strong>📈 Categorical Variables:</strong></p>
                <ul>
                    <li><strong>Frequency counts</strong> - Number of observations in each category</li>
                    <li><strong>Percentages</strong> - Proportion of total for each category</li>
                </ul>

                <p><strong>🔗 Bivariate Tables (GROUP BY):</strong></p>
                <ul>
                    <li><strong>Grouped statistics</strong> - Summary statistics for each group</li>
                    <li><strong>Overall column</strong> - Total statistics across all groups</li>
                    <li><strong>P-values</strong> - Statistical significance tests between groups</li>
                    <li><strong>Professional formatting</strong> - Publication-ready tables</li>
                </ul>

                <p><strong>Advanced TABULATE Features:</strong></p>
                <ul>
                    <li><strong>Multiple variables</strong> - Include multiple numeric and categorical variables</li>
                    <li><strong>WHERE clause</strong> - Filter data before creating summary table</li>
                    <li><strong>LIMIT clause</strong> - Limit number of data points for performance</li>
                    <li><strong>GROUP BY clause</strong> - Create bivariate tables with statistical tests</li>
                    <li><strong>Mixed data types</strong> - Automatically handles numeric and categorical variables</li>
                    <li><strong>HTML output</strong> - Beautiful, interactive tables in the browser</li>
                </ul>

                <p><strong>🔗 CORR Command (Correlation Analysis):</strong></p>
                <p>The <strong>VISUALIZE CORR</strong> command generates professional correlation heatmaps showing relationships between numeric variables.</p>

                <p><strong>📊 Correlation Features:</strong></p>
                <ul>
                    <li><strong>Pearson correlations</strong> - Standard correlation coefficients (-1 to +1)</li>
                    <li><strong>Diverging color palette</strong> - Blue (negative) → White (zero) → Red (positive)</li>
                    <li><strong>Annotated values</strong> - Correlation coefficients displayed to 2 decimal places</li>
                    <li><strong>Professional layout</strong> - Legend positioned above plot for clarity</li>
                    <li><strong>High-quality output</strong> - PNG format with 300 DPI resolution</li>
                </ul>

                <p><strong>📈 REGRESS Command (Regression Analysis):</strong></p>
                <p>The <strong>REGRESS</strong> command performs automatic regression analysis with intelligent model selection and data handling.</p>

                <p><strong>🤖 Automatic Model Selection:</strong></p>
                <ul>
                    <li><strong>Linear regression</strong> - For continuous outcome variables (lm)</li>
                    <li><strong>Logistic regression</strong> - For binary outcome variables (glm with binomial family)</li>
                    <li><strong>Binary detection</strong> - Recognizes 0/1, Yes/No, Y/N patterns automatically</li>
                    <li><strong>Coefficient interpretation</strong> - Raw coefficients for linear, odds ratios for logistic</li>
                </ul>

                <p><strong>⚡ Smart Data Processing:</strong></p>
                <ul>
                    <li><strong>Two-stage filtering</strong> - User filters first, then complete cases</li>
                    <li><strong>Random sampling</strong> - Automatic sampling up to 10,000 rows for performance</li>
                    <li><strong>Missing data handling</strong> - Removes rows with missing values in analysis variables</li>
                    <li><strong>Type conversion</strong> - Automatic numeric/categorical variable detection</li>
                </ul>

                <div class="alert alert-warning">
                    <strong>Requirements:</strong><br>
                    • <strong>VISUALIZE commands:</strong> R with ggplot2, dplyr, and viridis packages<br>
                    • <strong>TABULATE commands:</strong> R with gtsummary, dplyr, and gt packages<br>
                    • <strong>CORR commands:</strong> R with ggplot2, dplyr, and viridis packages<br>
                    • <strong>REGRESS commands:</strong> R with gtsummary, broom.helpers, and gt packages<br>
                    <br><small><strong>Install VISUALIZE packages:</strong> <code>install.packages(c("ggplot2", "dplyr", "viridis"))</code></small>
                    <br><small><strong>Install TABULATE packages:</strong> <code>install.packages(c("gtsummary", "dplyr", "gt"))</code></small>
                    <br><small><strong>Install CORR packages:</strong> <code>install.packages(c("ggplot2", "dplyr", "viridis"))</code></small>
                    <br><small><strong>Install REGRESS packages:</strong> <code>install.packages(c("gtsummary", "broom.helpers", "gt"))</code></small>
                    <br><small><strong>VISUALIZE/CORR Output:</strong> High-quality PNG images (300 DPI) for optimal rendering</small>
                    <br><small><strong>TABULATE/REGRESS Output:</strong> Professional HTML tables with statistical formatting</small>
                </div>

                <h6>Keyboard Shortcuts:</h6>
                <ul>
                    <li><kbd>Ctrl+Enter</kbd> - Run query</li>
                    <li><kbd>Ctrl+Shift+E</kbd> - Toggle SQL editor (collapse/expand)</li>
                    <li><kbd>Ctrl+A</kbd> - Select all</li>
                    <li><kbd>Ctrl+/</kbd> - Toggle comment</li>
                </ul>

                <div class="alert alert-success">
                    <strong><i class="fa fa-lightbulb-o me-1"></i>Tips:</strong>
                    <ul class="mb-0 mt-2">
                        <li>Click the SQL Editor header or use <kbd>Ctrl+Shift+E</kbd> to collapse the query box and focus on your results</li>
                        <li><strong>Large datasets (>1000 rows)</strong> automatically use paginated tables to prevent browser crashes</li>
                        <li>Paginated results include search, sorting, and adjustable page sizes for better performance</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/sql/sql.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap5.min.js"></script>
<script>
// Use standard SQL mode and add custom keyword highlighting via CSS
// This avoids interfering with string parsing
CodeMirror.defineMode("harmattan-sql", function(config, parserConfig) {
    // Get the base SQL mode
    const sqlMode = CodeMirror.getMode(config, "text/x-sql");

    return {
        startState: function() {
            return sqlMode.startState();
        },

        token: function(stream, state) {
            // Get the token from the base SQL mode
            const token = sqlMode.token(stream, state);

            // Check if this token is one of our custom keywords
            const word = stream.current();
            if (token === "keyword" || token === "variable-2") {
                if (/^(VISUALIZE|SUMMARIZE|DESCRIBE|BOXPLOT|BARCHART|SCATTERPLOT|TABULATE|CORR|REGRESS)$/i.test(word)) {
                    return "keyword custom-keyword";
                }
            }

            return token;
        },

        indent: sqlMode.indent ? function(state, textAfter) {
            return sqlMode.indent(state, textAfter);
        } : undefined,

        electricChars: sqlMode.electricChars,
        blockCommentStart: sqlMode.blockCommentStart,
        blockCommentEnd: sqlMode.blockCommentEnd,
        lineComment: sqlMode.lineComment
    };
});

// Initialize CodeMirror with custom mode
const editor = CodeMirror.fromTextArea(document.getElementById('sqlEditor'), {
    mode: 'harmattan-sql',
    theme: 'default',
    lineNumbers: true,
    lineWrapping: true,
    indentUnit: 2,
    smartIndent: true,
    extraKeys: {
        'Ctrl-Enter': runQuery,
        'Cmd-Enter': runQuery
    }
});

// Run query function
function runQuery() {
    const query = editor.getSelection() || editor.getValue();
    
    if (!query.trim()) {
        alert('Please enter a SQL query');
        return;
    }

    // Show loading
    document.getElementById('resultsContent').innerHTML = `
        <div class="loading">
            <i class="fa fa-spinner fa-spin fa-2x mb-3"></i>
            <p>Executing query...</p>
        </div>
    `;

    // Execute query
    fetch('/sql/execute', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query: query })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('Query response data:', data);

            // Check for visualization commands (including bivariate ones)
            const visualizationCommands = ['VISUALIZE', 'SCATTERPLOT', 'GROUPED_BOXPLOT', 'GROUPED_BARCHART', 'CORR'];

            if (visualizationCommands.includes(data.custom_command)) {
                console.log('Displaying visualization results');
                displayVisualizationResults(data);
            } else if (data.custom_command === 'DESCRIBE') {
                console.log('Displaying describe results');
                displayDescribeResults(data);
            } else if (data.custom_command === 'TABULATE') {
                console.log('Displaying tabulate results');
                displayTabulateResults(data);
            } else if (data.custom_command === 'REGRESS') {
                console.log('Displaying regress results');
                displayTabulateResults(data); // REGRESS uses same table display as TABULATE
            } else {
                // Always use DataTables with pagination for regular SQL queries
                console.log('Displaying DataTable results - total rows:', data.total_rows);
                console.log('DataTable data:', data);
                displayDataTableResults(data);
            }
        } else {
            console.log('Query error:', data.error);
            displayError(data.error);
        }
    })
    .catch(error => {
        displayError('Network error: ' + error.message);
    });
}



function displayDataTableResults(data) {
    console.log('displayDataTableResults called with data:', data);
    const { columns, total_rows, execution_time, query } = data;

    // Clean the query for pagination: remove trailing semicolon and any existing LIMIT clause
    const cleanQuery = query.replace(/;?\s*$/, '').replace(/\s+LIMIT\s+\d+(\s+OFFSET\s+\d+)?\s*$/i, '').trim();

    console.log('Original query:', query);
    console.log('Cleaned query for pagination:', cleanQuery);

    let html = `
        <div class="query-info">
            <strong>${total_rows.toLocaleString()}</strong> rows found in <strong>${execution_time}s</strong>
        </div>`;

    // Show original query if it had LIMIT clause that was removed
    if (query !== cleanQuery) {
        html += `
            <div class="alert alert-info mx-3 mt-2 mb-0">
                <small><strong>Original query:</strong> <code>${query}</code><br>
                <strong>Note:</strong> LIMIT clause removed for DataTable pagination</small>
            </div>
        `;
    }

    html += `
        <div class="pagination-info">
            <div>
                <strong>Interactive Table:</strong> Data is loaded dynamically with pagination and search
            </div>
            <div class="pagination-controls">
                <label class="page-size-selector">
                    Rows per page:
                    <select id="pageSizeSelect" class="form-select form-select-sm" style="width: auto; display: inline-block;">
                        <option value="10">10</option>
                        <option value="25" selected>25</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                        <option value="250">250</option>
                        <option value="500">500</option>
                        <option value="1000">1000</option>
                    </select>
                </label>
            </div>
        </div>
        <div class="table-container" style="width: 100%; overflow-x: auto;">
            <table id="paginatedTable" class="table table-sm table-striped results-table mb-0" style="width: 100%;">
                <thead>
                    <tr>
                        ${columns.map(col => `<th>${col}</th>`).join('')}
                    </tr>
                </thead>
                <tbody>
                    <!-- Data will be loaded via AJAX -->
                </tbody>
            </table>
        </div>
    `;

    document.getElementById('resultsContent').innerHTML = html;

    // Initialize DataTable with server-side processing
    const table = $('#paginatedTable').DataTable({
        processing: true,
        serverSide: true,
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100, 250, 500, 1000], [10, 25, 50, 100, 250, 500, 1000]],
        responsive: false, // Disable responsive to enable horizontal scrolling
        searching: true,
        ordering: true,
        scrollX: true, // Enable horizontal scrolling
        scrollCollapse: true, // Allow the table to collapse when there's less data
        autoWidth: false, // Disable automatic column width calculation
        columnDefs: [{
            targets: '_all',
            render: function(data, type, row, meta) {
                const cellData = row[meta.col];
                const displayData = cellData !== null ? cellData : '<em class="text-muted">NULL</em>';

                // For display type, truncate long text and add title for full content
                if (type === 'display' && typeof displayData === 'string' && displayData.length > 50) {
                    return `<span title="${displayData}">${displayData.substring(0, 50)}...</span>`;
                }
                return displayData;
            }
        }],
        ajax: {
            url: '/sql/execute_paginated',
            type: 'POST',
            contentType: 'application/json',
            data: function(d) {
                return JSON.stringify({
                    query: cleanQuery,
                    page: Math.floor(d.start / d.length) + 1,
                    page_size: d.length
                });
            },
            dataSrc: function(json) {
                if (json.success) {
                    return json.data;
                } else {
                    displayError(json.error);
                    return [];
                }
            }
        },
        columns: columns.map(col => ({ data: null, title: col })),
        language: {
            processing: '<div class="table-loading"><i class="fa fa-spinner fa-spin fa-2x mb-3"></i><p>Loading data...</p></div>',
            info: 'Showing _START_ to _END_ of _TOTAL_ rows',
            infoEmpty: 'No rows found',
            infoFiltered: '(filtered from _MAX_ total rows)',
            lengthMenu: 'Show _MENU_ rows per page',
            search: 'Search:',
            searchPlaceholder: 'Search in results...',
            paginate: {
                first: 'First',
                last: 'Last',
                next: 'Next',
                previous: 'Previous'
            },
            emptyTable: 'No data available in table'
        },
        dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
             '<"row"<"col-sm-12"tr>>' +
             '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
        drawCallback: function(settings) {
            // Update page size selector when DataTable page size changes
            $('#pageSizeSelect').val(settings._iDisplayLength);
        }
    });

    // Handle page size change
    $('#pageSizeSelect').on('change', function() {
        const newPageSize = parseInt($(this).val());
        table.page.len(newPageSize).draw();
    });

    // Store table reference for potential cleanup
    window.currentDataTable = table;
}

function displayError(error) {
    // Destroy existing DataTable if it exists
    if (window.currentDataTable) {
        window.currentDataTable.destroy();
        window.currentDataTable = null;
    }

    document.getElementById('resultsContent').innerHTML = `
        <div class="error-message">
            <strong>Error:</strong> ${error}
        </div>
    `;
}

function displayVisualizationResults(data) {
    // Safely extract properties with defaults
    const visualizations = data.visualizations || [];
    const column = data.column;
    const columns = data.columns;
    const table = data.table || '';
    const data_points = data.data_points || 0;
    const execution_time = data.execution_time || 0;
    const where_clause = data.where_clause;
    const limit_clause = data.limit_clause;
    const query_used = data.query_used || '';
    const custom_command = data.custom_command;
    const x_column = data.x_column;
    const y_column = data.y_column;
    const numeric_column = data.numeric_column;
    const group_column = data.group_column;
    const main_column = data.main_column;

    // Handle different command types
    let queryDescription = '';

    if (custom_command === 'SCATTERPLOT') {
        queryDescription = `<strong>${table}.${x_column} vs ${table}.${y_column}</strong>`;
    } else if (custom_command === 'GROUPED_BOXPLOT') {
        queryDescription = `<strong>${table}.${numeric_column} by ${table}.${group_column}</strong>`;
    } else if (custom_command === 'GROUPED_BARCHART') {
        queryDescription = `<strong>${table}.${main_column} by ${table}.${group_column}</strong>`;
    } else {
        // Handle single and multiple column cases
        const columnsList = columns || [column];
        const columnsDisplay = columnsList.join(', ');
        queryDescription = `<strong>${table}.${columnsDisplay}</strong>`;
    }
    let filterInfo = [];

    if (where_clause) {
        filterInfo.push(`WHERE: ${where_clause}`);
    }
    if (limit_clause) {
        filterInfo.push(`LIMIT: ${limit_clause}`);
    }

    if (filterInfo.length > 0) {
        queryDescription += ` (${filterInfo.join(', ')})`;
    }

    // Get command display name
    const commandDisplay = custom_command || 'VISUALIZE';

    let html = `
        <div class="query-info">
            <strong>${commandDisplay}</strong> command executed: <strong>${data_points}</strong> data points from ${queryDescription} in <strong>${execution_time}s</strong>
        </div>
    `;

    // Add query details if filters were used
    if (where_clause || limit_clause) {
        html += `
            <div class="alert alert-info mx-3 mt-2 mb-0">
                <small><strong>Query executed:</strong> <code>${query_used}</code></small>
            </div>
        `;
    }

    if (Array.isArray(visualizations) && visualizations.length > 0) {
        // Check visualization type
        const isMultiColumn = visualizations.some(viz => viz && viz.column !== undefined);
        const isBivariateCommand = ['SCATTERPLOT', 'GROUPED_BOXPLOT', 'GROUPED_BARCHART', 'CORR'].includes(custom_command);

        if (isBivariateCommand) {
            // Handle bivariate visualizations (scatterplot, grouped boxplot, grouped barchart)
            if (custom_command === 'GROUPED_BARCHART') {
                // Special 2-column layout for grouped barcharts
                html += '<div class="visualization-container bivariate-barchart">';
            } else {
                // Single visualization for scatterplot and grouped boxplot
                html += '<div class="visualization-container bivariate-single">';
            }

            visualizations.forEach(viz => {
                const imageContent = viz.svg || viz.png;
                const isBase64 = viz.png !== undefined;

                html += `
                    <div class="visualization-item">
                        <h6>${viz.title}</h6>
                        <div class="image-container" onclick="openFullscreen(this)">
                            ${isBase64 ?
                                `<img src="data:image/png;base64,${imageContent}" alt="${viz.title}" style="width: 100%; height: auto;">` :
                                imageContent
                            }
                        </div>
                    </div>
                `;
            });

            html += '</div>';
        } else if (isMultiColumn) {
            // Organize by plot type for multi-column (both numeric and categorical)
            const numericPlotTypes = ['histogram', 'boxplot', 'density', 'violin'];
            const numericPlotTitles = ['Histograms', 'Box Plots', 'Density Plots', 'Violin Plots'];
            const categoricalPlotTypes = ['bar_chart', 'pie_chart', 'doughnut_chart'];
            const categoricalPlotTitles = ['Bar Charts', 'Pie Charts', 'Doughnut Charts'];

            // Combine all plot types
            const allPlotTypes = [...numericPlotTypes, ...categoricalPlotTypes];
            const allPlotTitles = [...numericPlotTitles, ...categoricalPlotTitles];

            allPlotTypes.forEach((plotType, index) => {
                const plotsOfType = visualizations.filter(viz => viz.plot_type === plotType);

                if (plotsOfType.length > 0) {
                    html += `
                        <div class="plot-type-section">
                            <h5 class="plot-type-title">${allPlotTitles[index]}</h5>
                            <div class="visualization-container">
                    `;

                    plotsOfType.forEach(viz => {
                        const imageContent = viz.svg || viz.png;
                        const isBase64 = viz.png !== undefined;

                        html += `
                            <div class="visualization-item">
                                <h6>${viz.title}</h6>
                                <div class="image-container" onclick="openFullscreen(this)">
                                    ${isBase64 ?
                                        `<img src="data:image/png;base64,${imageContent}" alt="${viz.title}" style="width: 100%; height: auto;">` :
                                        imageContent
                                    }
                                </div>
                            </div>
                        `;
                    });

                    html += `
                            </div>
                        </div>
                    `;
                }
            });
        } else {
            // Single column visualization - use 3-column layout as requested
            html += '<div class="visualization-container single-variable">';

            visualizations.forEach(viz => {
                const imageContent = viz.svg || viz.png;
                const isBase64 = viz.png !== undefined;

                html += `
                    <div class="visualization-item">
                        <h6>${viz.title}</h6>
                        <div class="image-container" onclick="openFullscreen(this)">
                            ${isBase64 ?
                                `<img src="data:image/png;base64,${imageContent}" alt="${viz.title}" style="width: 100%; height: auto;">` :
                                imageContent
                            }
                        </div>
                    </div>
                `;
            });

            html += '</div>';
        }

        // Add fullscreen modal
        html += `
            <div class="modal fade" id="fullscreenModal" tabindex="-1">
                <div class="modal-dialog modal-fullscreen">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Visualization - Full Screen</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body d-flex justify-content-center align-items-center">
                            <div id="fullscreenContent"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    } else {
        html += '<div class="text-center py-4 text-muted">No visualizations generated.</div>';
    }

    document.getElementById('resultsContent').innerHTML = html;
}

function openFullscreen(element) {
    const svgContent = element.innerHTML;
    const modal = new bootstrap.Modal(document.getElementById('fullscreenModal'));
    document.getElementById('fullscreenContent').innerHTML = svgContent;

    // Scale SVG to fit fullscreen
    const svg = document.querySelector('#fullscreenContent svg');
    if (svg) {
        svg.style.width = '90vw';
        svg.style.height = '90vh';
        svg.style.maxWidth = '100%';
        svg.style.maxHeight = '100%';
    }

    modal.show();
}

function displayDescribeResults(data) {
    const { table, columns, row_count, execution_time } = data;

    let html = `
        <div class="query-info">
            <strong>DESCRIBE</strong> command executed: Table <strong>${table}</strong> with <strong>${columns.length}</strong> columns and <strong>${row_count}</strong> rows in <strong>${execution_time}s</strong>
        </div>
    `;

    if (columns && columns.length > 0) {
        html += `
            <div class="table-container">
                <table class="table table-sm table-striped results-table mb-0">
                    <thead>
                        <tr>
                            <th>Column Name</th>
                            <th>Data Type</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        columns.forEach(col => {
            const isNumeric = col.type.toLowerCase().includes('int') ||
                             col.type.toLowerCase().includes('float') ||
                             col.type.toLowerCase().includes('double') ||
                             col.type.toLowerCase().includes('decimal') ||
                             col.type.toLowerCase().includes('numeric');

            const isCategorical = !isNumeric; // All non-numeric columns are considered categorical

            html += `
                <tr>
                    <td><strong>${col.name}</strong></td>
                    <td>
                        <span class="badge bg-secondary">${col.type}</span>
                        ${isNumeric ? '<span class="badge bg-info ms-1">Numeric</span>' : '<span class="badge bg-warning ms-1">Categorical</span>'}
                    </td>
                    <td>
            `;

            if (isNumeric) {
                html += `
                    <button class="btn btn-sm btn-outline-primary" onclick="visualizeColumn('${col.name}', '${table}')">
                        <i class="fa fa-chart-bar"></i> Numeric Plots
                    </button>
                `;
            } else if (isCategorical) {
                html += `
                    <button class="btn btn-sm btn-outline-success" onclick="visualizeColumn('${col.name}', '${table}')">
                        <i class="fa fa-pie-chart"></i> Categorical Plots
                    </button>
                `;
            }

            html += `
                    </td>
                </tr>
            `;
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;
    } else {
        html += '<div class="text-center py-4 text-muted">No column information available.</div>';
    }

    document.getElementById('resultsContent').innerHTML = html;
}

function displayTabulateResults(data) {
    const { table_html, columns, table, data_points, execution_time, where_clause, group_by_clause, limit_clause, query_used, custom_command, predictor_vars, outcome_var } = data;

    // Build query description based on command type
    let queryDescription, commandName;

    if (custom_command === 'REGRESS') {
        commandName = 'REGRESS';
        const predictorsDisplay = predictor_vars ? predictor_vars.join(', ') : (columns ? columns.slice(0, -1).join(', ') : 'variables');
        const outcomeDisplay = outcome_var || (columns ? columns[columns.length - 1] : 'outcome');
        queryDescription = `<strong>${predictorsDisplay}</strong> → <strong>${outcomeDisplay}</strong> from <strong>${table}</strong>`;
    } else {
        commandName = 'TABULATE';
        const columnsDisplay = columns ? columns.join(', ') : 'variables';
        queryDescription = `<strong>${table}.${columnsDisplay}</strong>`;

        if (group_by_clause) {
            queryDescription += ` grouped by <strong>${group_by_clause}</strong>`;
        }
    }

    let filterInfo = [];
    if (where_clause) {
        filterInfo.push(`WHERE: ${where_clause}`);
    }
    if (limit_clause) {
        filterInfo.push(`LIMIT: ${limit_clause}`);
    }

    if (filterInfo.length > 0) {
        queryDescription += ` (${filterInfo.join(', ')})`;
    }

    let html = `
        <div class="query-info">
            <strong>${commandName}</strong> command executed: <strong>${data_points}</strong> data points from ${queryDescription} in <strong>${execution_time}s</strong>
        </div>
    `;

    // Add query details if filters were used
    if (where_clause || limit_clause || group_by_clause) {
        html += `
            <div class="alert alert-info mx-3 mt-2 mb-0">
                <small><strong>Query executed:</strong> <code>${query_used}</code></small>
            </div>
        `;
    }

    // Add the summary table using same structure as SELECT results
    if (table_html) {
        html += `
            <div class="table-container">
                <div class="table-summary-container">
                    ${table_html}
                </div>
            </div>
        `;
    } else {
        html += '<div class="text-center py-4 text-muted">No summary table generated.</div>';
    }

    document.getElementById('resultsContent').innerHTML = html;
}

function visualizeColumn(columnName, tableName) {
    const query = `VISUALIZE ${columnName} FROM ${tableName};`;
    editor.setValue(query);
    runQuery();
}

// Event listeners
document.getElementById('runQueryBtn').addEventListener('click', runQuery);
document.getElementById('clearBtn').addEventListener('click', () => {
    editor.setValue('');
    document.getElementById('resultsContent').innerHTML = `
        <div class="text-center py-5 text-muted">
            <i class="fa fa-code fa-2x mb-3"></i>
            <p>Run a SQL query to see results here</p>
        </div>
    `;
});

// Set focus to editor
editor.focus();



// Initialize collapsible editor functionality
setTimeout(function() {
    initializeCollapsibleEditor();
}, 100);

function initializeCollapsibleEditor() {
    const toggleEditorBtn = document.getElementById('toggleEditorBtn');
    const toggleIcon = document.getElementById('toggleIcon');
    const sqlEditorHeader = document.getElementById('sqlEditorHeader');
    const sqlEditorContent = document.getElementById('sqlEditorContent');
    const resultsPanel = document.querySelector('.results-panel');

    if (!toggleEditorBtn || !toggleIcon || !sqlEditorHeader || !sqlEditorContent || !resultsPanel) {
        return;
    }

    let isCollapsed = false;

    function toggleEditor() {
        isCollapsed = !isCollapsed;

        if (isCollapsed) {
            // Collapse the editor
            sqlEditorContent.classList.add('collapsed');
            sqlEditorHeader.classList.add('collapsed');
            toggleIcon.classList.add('rotated');
            resultsPanel.classList.add('expanded');
        } else {
            // Expand the editor
            sqlEditorContent.classList.remove('collapsed');
            sqlEditorHeader.classList.remove('collapsed');
            toggleIcon.classList.remove('rotated');
            resultsPanel.classList.remove('expanded');
            // Refocus editor when expanded
            setTimeout(() => editor.focus(), 300);
        }
    }

    // Add click handlers
    toggleEditorBtn.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        toggleEditor();
    });

    sqlEditorHeader.addEventListener('click', function(e) {
        // Only toggle if clicking on the header itself, not on buttons
        if (e.target === sqlEditorHeader || e.target.closest('.sql-editor-header') === sqlEditorHeader) {
            if (!e.target.closest('button') || e.target.closest('#toggleEditorBtn')) {
                toggleEditor();
            }
        }
    });

    // Add keyboard shortcut to toggle editor
    document.addEventListener('keydown', function(e) {
        // Ctrl+Shift+E to toggle editor
        if (e.ctrlKey && e.shiftKey && e.key === 'E') {
            e.preventDefault();
            toggleEditor();
        }
    });
}
</script>
{% endblock %}
