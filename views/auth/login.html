<!doctype html>
<html class="no-js " lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="Harmattan - Login">
    <title>Harmattan - Login</title>
    <link rel="icon" href="{{ url_for('static', filename='favicon.png') }}" type="image/png">

    <!-- project css file  -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/al.style.min.css') }}">
    <!-- project layout css file -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/layout.a.min.css') }}">
</head>

<body>

<div id="layout-a" class="theme-indigo">

    <!-- main body area -->
    <div class="main auth-div p-2 py-3 p-xl-5">

        <!-- Body: Body -->
        <div class="body d-flex p-0 p-xl-5">
            <div class="container-fluid">

                <div class="row g-0">
                    <div class="col-lg-6 d-none d-lg-flex justify-content-center align-items-center rounded-lg auth-h100">
                        <div style="max-width: 25rem;">
                            <div class="text-center mb-5">
                                <img src="{{ url_for('static', filename='images/logo.png') }}" alt="Harmattan" width="64" height="64" style="object-fit: contain;">
                            </div>

                            <div class="mb-5">
                                <h2 class="color-900">Harmattan</h2>
                            </div>

                            <!-- List Checked -->
                            <ul class="mb-5">
                                <li class="mb-4">
                                    <span class="d-block fw-bold">Unified Analytics Platform</span>
                                    <span class="text-muted">SQL Client, Jupyter Notebooks, and Data Visualization in one place</span>
                                </li>

                                <li>
                                    <span class="d-block fw-bold">Seamless Data Management</span>
                                    <span class="text-muted">Upload, query, and analyze your data with powerful tools</span>
                                </li>
                            </ul>

                            <div class="d-flex justify-content-between">
                                <div>
                                    <a href="#" class="me-2 text-muted"><i class="fa fa-facebook-square fa-lg"></i></a>
                                    <a href="#" class="me-2 text-muted"><i class="fa fa-github-square fa-lg"></i></a>
                                    <a href="#" class="me-2 text-muted"><i class="fa fa-linkedin-square fa-lg"></i></a>
                                    <a href="#" class="me-2 text-muted"><i class="fa fa-twitter-square fa-lg"></i></a>
                                </div>
                                <div>
                                    <a href="{{ url_for('auth.about') }}" class="me-2 text-muted">About</a>
                                    <a href="{{ url_for('auth.about') }}#contact" class="me-2 text-muted">Contact</a>
                                    <a href="{{ url_for('auth.about') }}#features" class="me-2 text-muted">Help</a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6 d-flex justify-content-center align-items-center border-0 rounded-lg auth-h100">
                        <div class="w-100 p-4 p-md-5 card border-0" style="max-width: 32rem;">
                            <!-- Form -->
                            <form class="row g-1 p-0 p-md-4" method="POST">
                                <div class="col-12 text-center mb-5">
                                    <h1>Sign in</h1>
                                    <span>Access to Harmattan Analytics Platform.</span>
                                </div>

                                <!-- Flash Messages -->
                                {% with messages = get_flashed_messages(with_categories=true) %}
                                    {% if messages %}
                                        {% for category, message in messages %}
                                            <div class="col-12">
                                                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                                    {{ message }}
                                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                                </div>
                                            </div>
                                        {% endfor %}
                                    {% endif %}
                                {% endwith %}

                                <div class="col-12">
                                    <div class="mb-2">
                                        <label class="form-label">Username</label>
                                        <input type="text" class="form-control form-control-lg" id="username" name="username"
                                               placeholder="Enter username" required>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="mb-2">
                                        <div class="form-label">
                                            <span class="d-flex justify-content-between align-items-center">
                                                Password
                                                <a class="text-primary" href="{{ url_for('auth.forgot_password') }}">Forgot Password?</a>
                                            </span>
                                        </div>
                                        <input type="password" class="form-control form-control-lg" id="password" name="password"
                                               placeholder="Enter password" required>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" value="" id="remember">
                                        <label class="form-check-label" for="remember">
                                            Remember me
                                        </label>
                                    </div>
                                </div>
                                <div class="col-12 text-center mt-4">
                                    <button type="submit" class="btn btn-lg btn-block btn-dark lift text-uppercase">SIGN IN</button>
                                </div>
                                <div class="col-12 text-center mt-4">
                                    <span class="text-muted">Default credentials: admin / admin</span>
                                </div>
                            </form>
                            <!-- End Form -->
                        </div>
                    </div>
                </div> <!-- End Row -->

            </div>
        </div>

    </div>

</div>

<!-- Jquery Core Js -->
<script src="{{ url_for('static', filename='bundles/libscripts.bundle.js') }}"></script>

<!-- Jquery Page Js -->
<script src="{{ url_for('static', filename='js/template.js') }}"></script>

</body>
</html>
