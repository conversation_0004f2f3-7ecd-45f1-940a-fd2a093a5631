<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Harmattan is a unified analytics platform that brings together SQL Client, Jupyter Notebooks, and Data Visualization in one powerful application.">
    <meta name="keywords" content="Harmattan, analytics, data science, SQL, Jupyter, notebooks, visualization, data analysis">
    <title>About Harmattan - Unified Analytics Platform</title>
    <link rel="icon" href="{{ url_for('static', filename='favicon.ico') }}" type="image/x-icon">

    <!-- Vendor CSS Files -->
    <link href="{{ url_for('static', filename='css/swiper.min.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/al.style.min.css') }}" rel="stylesheet">
    
    <!-- Template Main CSS File -->
    <link href="{{ url_for('static', filename='css/al.applanding.min.css') }}" rel="stylesheet">
</head>
<body>
<div id="mainDiv" class="theme-indigo">

<!-- header -->
<div class="section header">

    <nav class="navbar navbar-expand-lg navbar-light py-3">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('auth.about') }}">
                <span class="fs-3 fw-bold">Harmattan</span>
                <span class="text-muted small d-flex">Unified Analytics Platform</span>
            </a>
            <button class="navbar-toggler btn btn-white py-2 px-3" type="button" data-bs-toggle="collapse" data-bs-target="#mainnavbar">
                <span class="fa fa-bars"></span>
            </button>
            <div class="collapse navbar-collapse fs-6" id="mainnavbar">
                <ul class="navbar-nav ms-auto mb-2 mb-lg-0">
                    <li class="nav-item me-3"><a class="nav-link active" aria-current="page" href="#overview">Overview</a></li>
                    <li class="nav-item me-3"><a class="nav-link" href="#features">Features</a></li>
                    <li class="nav-item me-3"><a class="nav-link" href="#services">Services</a></li>
                    <li class="nav-item me-3"><a class="nav-link" href="#contact">Contact</a></li>
                </ul>
                <form class="d-flex ms-4">
                    <a href="{{ url_for('auth.login') }}" class="btn px-4 rounded-pill btn-dark">Get Started</a>
                </form>
            </div>
        </div>
    </nav>

    <div class="hero-img" id="overview">
        <div class="container">

            <div class="row g-3 justify-content-between">
                <div class="col-xl-4 col-lg-5">
                    <h1 class="display-5 color-900">Unified <span class="text-primary fw-bold">Analytics Platform</span> for Data Scientists!</h1>
                    <p class="lead">Build powerful data insights with SQL queries, Jupyter notebooks, and interactive visualizations all in one seamless platform.</p>
                    <div class="my-4 py-4">
                        <div class="d-grid gap-2">
                            <a href="{{ url_for('auth.login') }}" class="btn btn-lg btn-primary">START ANALYZING</a>
                        </div>
                        <div class="mt-3 text-center">
                            <small class="text-muted">Free to use • No installation required • Web-based</small>
                        </div>
                    </div>
                    <ul class="social mb-0 list-inline mt-3">
                        <li class="list-inline-item"><a href="#" class="pe-3 text-muted"><i class="fa fa-globe"></i></a></li>
                        <li class="list-inline-item"><a href="#" class="pe-3 text-muted"><i class="fa fa-linkedin"></i></a></li>
                        <li class="list-inline-item"><a href="#" class="pe-3 text-muted"><i class="fa fa-github"></i></a></li>
                        <li class="list-inline-item"><a href="#" class="pe-3 text-muted"><i class="fa fa-twitter"></i></a></li>
                    </ul>
                </div>
                <div class="col-xl-6 col-lg-7">
                    <svg class="img-fluid" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1350 1140" version="1.1">
                        <line x1="1064.4" y1="1137.6" x2="1347.8" y2="1137.6" stroke="#CED5E5" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
                        <line x1="481.3" y1="1127.4" x2="731.3" y2="1127.4" stroke="#CED5E5" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
                        <polygon fill="#CED5E5" fill-rule="nonzero" points="308.5 1038.3 308.5 1038.3 308.5 912.6 260.2 912.6 260.2 1038.3 127 1073 127 1091.5 284.4 1073 441.7 1091.5 441.7 1073"/>
                        <path d="M477.6,1003 L464.4,1046.4 C464.4,1046.4 469.9,1069.8 490.8,1069.6 C511.7,1069.4 508.4,1061.9 508.4,1061.9 L529.7,1009.1 L477.6,1003 Z" fill="#FFDFD7" fill-rule="nonzero"/>
                        <path class="fill-primary" d="M466.6,1039.5 C466.6,1039.5 463.1,1039.6 460.3,1046.7 C457.5,1053.8 446.4,1079.2 446.4,1079.2 L511.6,1119.4 L579.7,1125.6 C579.7,1125.6 591.3,1119.5 583.1,1106.9 C574.9,1094.4 533.9,1081.5 533.9,1081.5 L512.4,1055.1 C512.4,1055.1 499,1057.7 484.8,1052.7 C470.5,1047.8 466.6,1039.5 466.6,1039.5 Z" fill="#FE8163" fill-rule="nonzero"/>
                        <path d="M400.7,108.4 C400.7,108.4 363.8,251 473.2,356 C582.6,461 718,480.5 734.2,559.6 C750.4,638.7 724.2,728.7 674.6,796 C624.9,863.3 548.9,952.9 674.6,1003.1 C800.3,1053.3 920.3,1016.3 988.9,951.7 C1057.5,887.1 1069.4,802.2 1002.1,729.3 C934.8,656.3 925.6,635.2 958.6,521.8 C991.6,408.4 1007.4,353 895.3,331.9 C783.2,310.8 697,391.2 639.2,226.4 C581.4,61.6 547.1,-21.7 479.8,6 C412.6,33.7 400.7,108.4 400.7,108.4 Z" fill="#F1F2F7" fill-rule="nonzero"/>
                        <polygon fill="#CED5E5" fill-rule="nonzero" points="1219.1 939.3 1193.1 939.3 1199.3 719.5 1212.9 719.5"/>
                        <polygon fill="#A5ACBA" fill-rule="nonzero" points="695.3 1073.8 674.6 1073.8 653.9 1073.8 546.5 1104 546.5 1114.2 674.6 1114.2 802.7 1114.2 802.7 1104"/>
                        <circle class="fill-primary" fill="#008AFF" fill-rule="nonzero" cx="13.4" cy="248.2" r="13.2"/>
                        <circle fill="#FFC933" fill-rule="nonzero" cx="777.3" cy="74.9" r="10.3"/>
                        <circle class="fill-primary" fill="#008AFF" fill-rule="nonzero" cx="639.2" cy="28.2" r="14.7"/>
                        <circle class="fill-primary" fill="#008AFF" fill-rule="nonzero" cx="235.1" cy="18.6" r="12.6"/>
                    </svg>
                </div>
            </div>

        </div>
    </div>

    <!-- Animation line -->
    <div class="animate_lines">
        <div class="line"></div>
        <div class="line"></div>
        <div class="line"></div>
        <div class="line"></div>
        <div class="line"></div>
        <div class="line"></div>
    </div>
</div>

<!-- fun fact -->
<div class="section facts py-4 bg-primary">
    <div class="container">
        
        <div class="row row-cols-lg-4 row-cols-md-2 row-cols-sm-2 row-cols-1 g-1 text-center text-white">
            <div class="col">
                <div class="card border-0 bg-transparent">
                    <div class="card-body">
                        <h2 class="fw-bold">SQL</h2>
                        <h6 class="mb-0">Query Engine</h6>
                    </div>
                </div>
            </div>
            <div class="col">
                <div class="card border-0 bg-transparent">
                    <div class="card-body">
                        <h2 class="fw-bold">Jupyter</h2>
                        <h6 class="mb-0">Notebooks</h6>
                    </div>
                </div>
            </div>
            <div class="col">
                <div class="card border-0 bg-transparent">
                    <div class="card-body">
                        <h2 class="fw-bold">Data</h2>
                        <h6 class="mb-0">Visualization</h6>
                    </div>
                </div>
            </div>
            <div class="col">
                <div class="card border-0 bg-transparent">
                    <div class="card-body">
                        <h2 class="fw-bold">Web</h2>
                        <h6 class="mb-0">Based</h6>
                    </div>
                </div>
            </div>
        </div><!-- .row end -->

    </div>
</div>

<!-- features -->
<div class="section features" id="features">
    <div class="container">

        <div class="row justify-content-center text-center mb-5">
            <div class="col-lg-8">
                <span class="bg-dark px-3 py-2 color-fff">Powerful Features</span>
                <h2 class="mt-4 color-900">What Makes Harmattan Different?</h2>
                <p class="fs-6">A comprehensive analytics platform designed for modern data workflows.</p>
            </div>
        </div><!-- .row end -->
        <div class="row row-cols-lg-3 row-cols-md-2 row-cols-sm-2 row-cols-1 g-1 text-center">
            <div class="col">
                <div class="card p-4">
                    <div class="card-body">
                        <span class="bg-primary text-white p-3 rounded fa fa-database fa-lg"></span>
                        <h5 class="color-900 mt-4">SQL Client</h5>
                        <p class="text-muted mb-0">Execute complex queries with our powerful SQL interface supporting multiple database formats</p>
                    </div>
                </div>
            </div>
            <div class="col">
                <div class="card p-4">
                    <div class="card-body">
                        <span class="bg-warning text-light p-3 rounded fa fa-book fa-lg"></span>
                        <h5 class="color-900 mt-4">Jupyter Notebooks</h5>
                        <p class="text-muted mb-0">Interactive data analysis with full Python and R support in embedded notebooks</p>
                    </div>
                </div>
            </div>
            <div class="col">
                <div class="card p-4">
                    <div class="card-body">
                        <span class="bg-dark text-white p-3 rounded fa fa-bar-chart fa-lg"></span>
                        <h5 class="color-900 mt-4">Data Visualization</h5>
                        <p class="text-muted mb-0">Create stunning charts and graphs with our integrated visualization tools</p>
                    </div>
                </div>
            </div>
            <div class="col">
                <div class="card p-4">
                    <div class="card-body">
                        <span class="chart-color4 text-light p-3 rounded fa fa-upload fa-lg"></span>
                        <h5 class="color-900 mt-4">Data Upload</h5>
                        <p class="text-muted mb-0">Support for multiple file formats including CSV, Excel, Parquet, and more</p>
                    </div>
                </div>
            </div>
            <div class="col">
                <div class="card p-4">
                    <div class="card-body">
                        <span class="chart-color2 text-light p-3 rounded fa fa-table fa-lg"></span>
                        <h5 class="color-900 mt-4">Data Tables</h5>
                        <p class="text-muted mb-0">Browse and manage your datasets with intuitive table interfaces</p>
                    </div>
                </div>
            </div>
            <div class="col">
                <div class="card p-4">
                    <div class="card-body">
                        <span class="chart-color3 text-light p-3 rounded fa fa-cogs fa-lg"></span>
                        <h5 class="color-900 mt-4">Custom Analytics</h5>
                        <p class="text-muted mb-0">Build custom analysis workflows with our flexible SQCustom language</p>
                    </div>
                </div>
            </div>
        </div><!-- .row end -->

    </div>
</div>

<!-- app intro -->
<div class="section app-intro">
    <div class="container">

        <div class="row row-grid justify-content-between align-items-center py-5">
            <div class="col-lg-5 order-lg-2">
                <h3 class="color-900">Need a powerful analytics platform for your data?</h3>
                <p class="lead my-3">Harmattan combines the best of SQL querying, notebook computing, and data visualization in one unified platform</p>
                <ul class="resume-box">
                    <li class="mb-4">
                        <div class="icon text-center"><i class="fa fa-database"></i></div>
                        <h6 class="color-900">Why choose Harmattan?</h6>
                        <span>Seamlessly integrate data analysis workflows without switching between multiple tools and platforms</span>
                    </li>
                    <li>
                        <div class="icon text-center"><i class="fa fa-cubes"></i></div>
                        <h6 class="color-900">What is SQCustom?</h6>
                        <span>Our custom SQL extension language that adds powerful visualization and analytics capabilities to standard SQL</span>
                    </li>
                </ul>
            </div>
            <div class="col-lg-6 order-lg-1">
                <div class="card bg-transparent me-lg-5 shadow-sm">
                    <div class="card-body p-1"><img class="img-fluid rounded" src="{{ url_for('static', filename='images/applanding/screen-1.png') }}" alt="Harmattan SQL Client"></div>
                </div>
            </div>
        </div><!-- .row end -->
        <div class="row row-grid justify-content-between align-items-center py-5">
            <div class="col-lg-5">
                <h3  class="color-900">A modern <strong>Analytics</strong> Platform for Data Scientists</h3>
                <p class="lead my-4">Built with modern web technologies to provide a seamless experience for data analysis and visualization workflows.</p>
                <ul class="list-unstyled mb-0">
                    <li class="py-2">
                        <i class="fa fa-check-circle me-3"></i><span class="h6 mb-0">Perfect for data scientists and analysts</span>
                    </li>
                    <li class="py-2">
                        <i class="fa fa-check-circle me-3"></i><span class="h6 mb-0">Web-based with no installation required</span>
                    </li>
                    <li class="py-2">
                        <i class="fa fa-check-circle me-3"></i><span class="h6 mb-0">Supports multiple data formats and sources</span>
                    </li>
                </ul>
            </div>
            <div class="col-lg-6">
                <div class="card mb-0 ms-lg-5">
                    <div class="card-body p-1"><img class="img-fluid rounded" src="{{ url_for('static', filename='images/applanding/screen-2.png') }}" alt="Harmattan Notebooks"></div>
                </div>
            </div>
        </div><!-- .row end -->

    </div>
</div>

<!-- call to action -->
<div class="section call-action">
    <div class="container">

        <div class="row justify-content-center text-center text-white">
            <div class="col-lg-7 col-12">
                <span class="bg-white px-3 py-2 text-dark">GETTING STARTED</span>
                <h2 class="mb-3 mt-4">Start Your Data Analysis Journey</h2>
                <p class="lead">Join data scientists and analysts who use Harmattan to streamline their workflows and create powerful insights from their data.</p>
                <div class="mt-5">
                    <a href="{{ url_for('auth.login') }}" class="btn lift btn-lg px-5 btn-dark fs-6">Get Started</a>
                    <a href="#features" class="btn lift btn-lg px-5 btn-primary fs-6">Learn More</a>
                </div>
            </div>
        </div><!-- .row end -->

    </div>
</div>

<!-- services -->
<div class="section services" id="services">
    <div class="container">

        <div class="row justify-content-center text-center mb-5">
            <div class="col-lg-8">
                <span class="bg-dark px-3 py-2 color-fff">Core Services</span>
                <h2 class="mb-3 mt-4 color-900">Comprehensive Analytics Tools</h2>
                <p class="fs-6">Everything you need for modern data analysis in one integrated platform.</p>
            </div>
        </div><!-- .row end -->
        <div class="row g-1 mt-5 row-deck">
            <div class="col-lg-4 col-md-12">
                <div class="card border-0 text-center p-lg-4">
                    <div class="card-body">
                        <div class="mb-5"><img src="{{ url_for('static', filename='images/applanding/service-1.svg') }}" class="img-fluid img-center" style="height:160px" alt="SQL Analytics"></div>
                        <h4 class="color-900 mb-3">SQL Analytics</h4>
                        <p class="fs-6 text-muted mb-0">Execute complex queries with our advanced SQL client supporting multiple database formats and custom extensions</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-12">
                <div class="card border-0 text-center p-lg-4">
                    <div class="card-body">
                        <div class="mb-5"><img src="{{ url_for('static', filename='images/applanding/service-2.svg') }}" class="img-fluid img-center" style="height:160px" alt="Interactive Notebooks"></div>
                        <h4 class="color-900 mb-3">Interactive Notebooks</h4>
                        <p class="fs-6 text-muted mb-0">Full-featured Jupyter notebook environment with Python and R support for advanced data science workflows</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-12">
                <div class="card border-0 text-center p-lg-4">
                    <div class="card-body">
                        <div class="mb-5"><img src="{{ url_for('static', filename='images/applanding/service-3.svg') }}" class="img-fluid img-center" style="height:160px" alt="Data Visualization"></div>
                        <h4 class="color-900 mb-3">Data Visualization</h4>
                        <p class="fs-6 text-muted mb-0">Create stunning charts, graphs, and dashboards with our integrated visualization tools and libraries</p>
                    </div>
                </div>
            </div>
        </div><!-- .row end -->

    </div>
</div>

<!-- footer -->
<div class="section footer" id="contact">

    <div class="section footer-middle py-6">
        <div class="container">
            <div class="row g-3">
                <div class="col-lg-4 col-12">
                    <div class="d-flex align-items-center mb-2">
                        <h3 class="mb-0 fw-bold">Harmattan</h3>
                    </div>
                    <span class="lead">Unified Analytics Platform for modern data science workflows.</span>
                    <ul class="mt-4 address">
                        <li><span>Platform:</span> Web-based Analytics</li>
                        <li><span>Support:</span> <EMAIL></li>
                        <li><span>Access:</span> 24/7 Available</li>
                    </ul>
                    <ul class="list-unstyled d-flex">
                        <li><a class="p-2 me-2" href="#"><i class="fa fa-github"></i></a></li>
                        <li><a class="p-2 me-2" href="#"><i class="fa fa-twitter"></i></a></li>
                        <li><a class="p-2 me-2" href="#"><i class="fa fa-linkedin"></i></a></li>
                        <li><a class="p-2 me-2" href="#"><i class="fa fa-globe"></i></a></li>
                    </ul>
                </div>
                <div class="col-lg-8 col-12">
                    <div class="row g-3">
                        <div class="col-lg-4 col-md-6 col-12">
                            <h5 class="mb-4 color-900">Platform</h5>
                            <ul class="footer-link">
                                <li><a href="{{ url_for('auth.about') }}">About</a></li>
                                <li><a href="#features">Features</a></li>
                                <li><a href="#services">Services</a></li>
                                <li><a href="{{ url_for('auth.login') }}">Login</a></li>
                            </ul>
                        </div>
                        <div class="col-lg-4 col-md-6 col-12">
                            <h5 class="mb-4 color-900">Tools</h5>
                            <ul class="footer-link">
                                <li><a href="#">SQL Client</a></li>
                                <li><a href="#">Jupyter Notebooks</a></li>
                                <li><a href="#">Data Visualization</a></li>
                                <li><a href="#">File Upload</a></li>
                                <li><a href="#">Data Tables</a></li>
                                <li><a href="#">Custom Analytics</a></li>
                            </ul>
                        </div>
                        <div class="col-lg-4 col-12">
                            <h5 class="mb-4 color-900">Get Started</h5>
                            <p class="text-muted">Ready to start analyzing your data?</p>
                            <div class="d-grid gap-2">
                                <a href="{{ url_for('auth.login') }}" class="btn btn-primary">Access Platform</a>
                            </div>
                            <div class="mt-3">
                                <small class="text-muted">No installation required • Web-based • Free to use</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div><!-- .row end -->
        </div>
    </div>
    <div class="footer-bottom border-top py-4">
        <div class="container">

            <div class="row g-3">
                <div class="col-lg-6 col-md-6 col-12">
                    <span>Harmattan Analytics Platform &copy; 2024</span>
                </div>
                <div class="col-lg-6 col-md-6 col-12">
                    <ul class="list-unstyled d-flex flex-wrap justify-content-md-end mb-0">
                        <li><a class="p-2" href="#">Terms of use</a></li>
                        <li><a class="p-2" href="#">Privacy Policy</a></li>
                        <li><a class="p-2" href="#">Help</a></li>
                        <li><a class="p-2" href="#contact">Contact</a></li>
                    </ul>
                </div>
            </div><!-- .row end -->

        </div>
    </div>

</div>

<div id="preloader"></div>
<a href="#" class="back-to-top d-flex align-items-center justify-content-center"><i class="fa fa-arrow-up"></i></a>

</div>

<!-- Bootstrap JS Files -->
<script src="{{ url_for('static', filename='bundles/libscripts.bundle.js') }}"></script>

<!-- Vendor JS Files -->
<script src="{{ url_for('static', filename='bundles/swiper.bundle.js') }}"></script>

<!-- Template Main JS File -->
<script src="{{ url_for('static', filename='js/template.js') }}"></script>
</body>
</html>
