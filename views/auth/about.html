<!doctype html>
<html class="no-js " lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="Harmattan - About">
    <title>About Harmattan - Unified Analytics Platform</title>
    <link rel="icon" href="{{ url_for('static', filename='favicon.png') }}" type="image/png">

    <!-- project css file  -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/al.style.min.css') }}">
    <!-- project layout css file -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/layout.a.min.css') }}">
</head>

<body>

<div id="layout-a" class="theme-indigo">

    <!-- main body area -->
    <div class="main p-2 py-3 p-xl-5">

        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-light mb-4">
            <div class="container-fluid">
                <a class="navbar-brand d-flex align-items-center" href="{{ url_for('auth.about') }}">
                    <img src="{{ url_for('static', filename='images/logo.png') }}" alt="Harmattan" width="40" height="40" class="me-3" style="object-fit: contain;">
                    <div>
                        <span class="fs-3 fw-bold">Harmattan</span>
                        <span class="text-muted small d-block">Unified Analytics Platform</span>
                    </div>
                </a>
                <div class="ms-auto">
                    <a href="{{ url_for('auth.login') }}" class="btn btn-primary">Get Started</a>
                </div>
            </div>
        </nav>

        <!-- Hero Section -->
        <div class="container">
            <div class="row justify-content-center text-center py-5">
                <div class="col-lg-8">
                    <h1 class="display-4 mb-4">About <span class="text-primary">Harmattan</span></h1>
                    <p class="lead mb-4">Unified Analytics Platform for Data Scientists</p>
                    <p class="fs-5 text-muted">Build powerful data insights with SQL queries, Jupyter notebooks, and interactive visualizations all in one seamless platform.</p>
                    <div class="mt-4">
                        <a href="{{ url_for('auth.login') }}" class="btn btn-primary btn-lg me-3">Get Started</a>
                        <a href="#features" class="btn btn-outline-primary btn-lg">Learn More</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Features Section -->
        <div class="container py-5" id="features">
            <div class="row justify-content-center text-center mb-5">
                <div class="col-lg-8">
                    <h2 class="mb-4">Platform Features</h2>
                    <p class="lead text-muted">Everything you need for modern data analysis</p>
                </div>
            </div>

            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center p-4">
                            <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                                <i class="fa fa-database fa-lg"></i>
                            </div>
                            <h5 class="card-title">SQL Client</h5>
                            <p class="card-text text-muted">Execute complex queries with our powerful SQL interface supporting multiple database formats</p>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center p-4">
                            <div class="bg-warning text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                                <i class="fa fa-book fa-lg"></i>
                            </div>
                            <h5 class="card-title">Jupyter Notebooks</h5>
                            <p class="card-text text-muted">Interactive data analysis with full Python and R support in embedded notebooks</p>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center p-4">
                            <div class="bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                                <i class="fa fa-bar-chart fa-lg"></i>
                            </div>
                            <h5 class="card-title">Data Visualization</h5>
                            <p class="card-text text-muted">Create stunning charts and graphs with our integrated visualization tools</p>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center p-4">
                            <div class="bg-info text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                                <i class="fa fa-upload fa-lg"></i>
                            </div>
                            <h5 class="card-title">Data Upload</h5>
                            <p class="card-text text-muted">Support for multiple file formats including CSV, Excel, Parquet, and more</p>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center p-4">
                            <div class="bg-secondary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                                <i class="fa fa-table fa-lg"></i>
                            </div>
                            <h5 class="card-title">Data Tables</h5>
                            <p class="card-text text-muted">Browse and manage your datasets with intuitive table interfaces</p>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body text-center p-4">
                            <div class="bg-dark text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                                <i class="fa fa-cogs fa-lg"></i>
                            </div>
                            <h5 class="card-title">Custom Analytics</h5>
                            <p class="card-text text-muted">Build custom analysis workflows with our flexible SQCustom language</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Call to Action -->
        <div class="bg-primary text-white py-5">
            <div class="container">
                <div class="row justify-content-center text-center">
                    <div class="col-lg-8">
                        <h2 class="mb-3">Ready to Get Started?</h2>
                        <p class="lead mb-4">Join data scientists and analysts who use Harmattan to streamline their workflows and create powerful insights from their data.</p>
                        <a href="{{ url_for('auth.login') }}" class="btn btn-light btn-lg">Access Platform</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <footer class="bg-dark text-light py-4">
            <div class="container">
                <div class="row">
                    <div class="col-lg-6">
                        <h5>Harmattan</h5>
                        <p class="text-muted">Unified Analytics Platform for modern data science workflows.</p>
                    </div>
                    <div class="col-lg-6 text-lg-end">
                        <p class="text-muted mb-0">&copy; 2024 Harmattan Analytics Platform</p>
                        <div class="mt-2">
                            <a href="{{ url_for('auth.login') }}" class="text-light me-3">Login</a>
                            <a href="{{ url_for('auth.about') }}" class="text-light me-3">About</a>
                            <a href="#features" class="text-light">Features</a>
                        </div>
                    </div>
                </div>
            </div>
        </footer>

    </div>

</div>

<!-- Bootstrap JS Files -->
<script src="{{ url_for('static', filename='bundles/libscripts.bundle.js') }}"></script>

<!-- Template Main JS File -->
<script src="{{ url_for('static', filename='js/template.js') }}"></script>
</body>
</html>
