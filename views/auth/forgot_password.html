<!doctype html>
<html class="no-js " lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="Harmattan - Forgot Password">
    <title>Harmattan - Forgot Password</title>
    <link rel="icon" href="{{ url_for('static', filename='favicon.png') }}" type="image/png">

    <!-- project css file  -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/al.style.min.css') }}">
    <!-- project layout css file -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/layout.a.min.css') }}">
</head>

<body>

<div id="layout-a" class="theme-indigo">

    <!-- main body area -->
    <div class="main auth-div p-2 py-3 p-xl-5">
        
        <!-- Body: Body -->
        <div class="body d-flex p-0 p-xl-5">
            <div class="container-fluid">

                <div class="row g-0">
                    <div class="col-lg-6 d-none d-lg-flex justify-content-center align-items-center rounded-lg auth-h100">
                        <div style="max-width: 25rem;">
                            <div class="text-center mb-5">
                                <img src="{{ url_for('static', filename='images/logo.png') }}" alt="Harmattan" width="64" height="64" style="object-fit: contain;">
                            </div>
                
                            <div class="mb-5">
                                <h2 class="color-900">Harmattan</h2>
                            </div>
                
                            <!-- List Checked -->
                            <ul class="mb-5">
                                <li class="mb-4">
                                    <span class="d-block fw-bold">Unified Analytics Platform</span>
                                    <span class="text-muted">SQL Client, Jupyter Notebooks, and Data Visualization in one place</span>
                                </li>
                
                                <li>
                                    <span class="d-block fw-bold">Seamless Data Management</span>
                                    <span class="text-muted">Upload, query, and analyze your data with powerful tools</span>
                                </li>
                            </ul>

                            <div class="ms-4 ps-2">
                                <a href="#" class="me-2 text-muted"><i class="fa fa-facebook-square fa-lg"></i></a>
                                <a href="#" class="me-2 text-muted"><i class="fa fa-github-square fa-lg"></i></a>
                                <a href="#" class="me-2 text-muted"><i class="fa fa-linkedin-square fa-lg"></i></a>
                                <a href="#" class="me-2 text-muted"><i class="fa fa-twitter-square fa-lg"></i></a>
                            </div>
                            <div class="mt-3">
                                <a href="{{ url_for('auth.about') }}" class="text-muted">Learn more about Harmattan</a>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6 d-flex justify-content-center align-items-center border-0 rounded-lg auth-h100">
                        <div class="w-100 p-4 p-md-5 card border-0" style="max-width: 32rem;">
                            <!-- Form -->
                            <form class="row g-1 p-0 p-md-4" method="POST">
                                <div class="col-12 text-center mb-5">
                                    <img src="{{ url_for('static', filename='images/auth-password-reset.svg') }}" class="w240 mb-4" alt="Password Reset" />
                                    <h1>Forgot password?</h1>
                                    <span>Enter the email address you used when you joined and we'll send you instructions to reset your password.</span>
                                </div>
                                
                                <!-- Flash Messages -->
                                {% with messages = get_flashed_messages(with_categories=true) %}
                                    {% if messages %}
                                        {% for category, message in messages %}
                                            <div class="col-12">
                                                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                                    {{ message }}
                                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                                </div>
                                            </div>
                                        {% endfor %}
                                    {% endif %}
                                {% endwith %}

                                <div class="col-12">
                                    <div class="mb-2">
                                        <label class="form-label">Email address</label>
                                        <input type="email" class="form-control form-control-lg" id="email" name="email" 
                                               placeholder="<EMAIL>" required>
                                    </div>
                                </div>
                                <div class="col-12 text-center mt-4">
                                    <button type="submit" class="btn btn-lg btn-block btn-dark lift text-uppercase">SUBMIT</button>
                                </div>
                                <div class="col-12 text-center mt-4">
                                    <span class="text-muted"><a href="{{ url_for('auth.login') }}">Back to Sign in</a></span>
                                </div>
                            </form>
                            <!-- End Form -->
                        </div>
                    </div>
                </div> <!-- End Row -->
                
            </div>
        </div>

    </div>

</div>

<!-- Jquery Core Js -->
<script src="{{ url_for('static', filename='bundles/libscripts.bundle.js') }}"></script>

<!-- Jquery Page Js -->
<script src="{{ url_for('static', filename='js/template.js') }}"></script>

</body>
</html>
