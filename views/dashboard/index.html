{% extends "base/harmattan.html" %}

{% block title %}Dashboard - Harmattan{% endblock %}

{% block header %}
<small class="text-muted">Welcome back</small>
<h1 class="h4 mt-1">Dashboard</h1>
{% endblock %}

{% block header_actions %}
<a href="{{ url_for('auth.about') }}" title="About" target="_blank" class="btn btn-white border lift">About</a>
<button type="button" class="btn btn-dark lift" data-bs-toggle="modal" data-bs-target="#uploadModal">Upload Data</button>
{% endblock %}

{% block content %}
<!-- Statistics Cards -->
<div class="row g-2 mb-4 row-deck">
    <div class="col-xl-3 col-lg-4 col-md-6">
        <div class="card">
            <div class="card-body">
                <i class="fa fa-database fa-lg position-absolute top-0 end-0 p-3"></i>
                <div class="mb-2 text-uppercase">Total Tables</div>
                <div><span class="h4">{{ tables|length }}</span></div>
                <small class="text-muted">Database tables available</small>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-lg-4 col-md-6">
        <div class="card">
            <div class="card-body">
                <i class="fa fa-table fa-lg position-absolute top-0 end-0 p-3"></i>
                <div class="mb-2 text-uppercase">Total Rows</div>
                <div><span class="h4">
                {% if tables %}
                    {% set row_counts = tables | selectattr('row_count', 'number') | map(attribute='row_count') | list %}
                    {{ "{:,}".format(row_counts | sum) }}
                {% else %}
                    0
                {% endif %}
                </span></div>
                <small class="text-muted">Across all tables</small>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-lg-4 col-md-6">
        <div class="card">
            <div class="card-body">
                <i class="fa fa-hdd-o fa-lg position-absolute top-0 end-0 p-3"></i>
                <div class="mb-2 text-uppercase">Storage Used</div>
                <div><span class="h4">
                {% if tables %}
                    {% set file_sizes = tables | selectattr('file_size_mb', 'number') | map(attribute='file_size_mb') | list %}
                    {{ "%.1f"|format(file_sizes | sum) }}
                {% else %}
                    0.0
                {% endif %}
                MB</span></div>
                <small class="text-muted">Total data storage</small>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-lg-4 col-md-6">
        <div class="card">
            <div class="card-body">
                <i class="fa fa-book fa-lg position-absolute top-0 end-0 p-3"></i>
                <div class="mb-2 text-uppercase">Total Notebooks</div>
                <div><span class="h4">{{ notebook_count }}</span></div>
                <small class="text-muted">Jupyter notebooks available</small>
            </div>
        </div>
    </div>

</div>

<!-- Tables List -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">Database Tables</h6>
            </div>
            <div class="card-body">
                {% if tables %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Table Name</th>
                                <th>Rows</th>
                                <th>Size</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for table in tables %}
                            <tr>
                                <td>
                                    <i class="fa fa-table me-2"></i>
                                    <strong>{{ table.name }}</strong>
                                </td>
                                <td>{{ "{:,}".format(table.row_count) if table.row_count != 'Error' else 'Error' }}</td>
                                <td>{{ table.file_size_mb }} MB</td>
                                <td>
                                    <a href="{{ url_for('dashboard.view_table', table_name=table.name) }}" 
                                       class="btn btn-sm btn-outline-primary me-1">
                                        <i class="fa fa-eye"></i> View
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                            onclick="confirmDelete('{{ table.name }}')">
                                        <i class="fa fa-trash"></i> Delete
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fa fa-database fa-3x text-muted mb-3"></i>
                    <h5>No tables found</h5>
                    <p class="text-muted">Upload your first dataset to get started</p>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
                        <i class="fa fa-upload me-1"></i> Upload Data
                    </button>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Upload Modal -->
<div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Upload Data File</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('dashboard.upload_file') }}" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="table_name" class="form-label">Table Name</label>
                        <input type="text" class="form-control" id="table_name" name="table_name" 
                               placeholder="Enter table name" required>
                        <div class="form-text">Table name will be converted to lowercase</div>
                    </div>
                    <div class="mb-3">
                        <label for="file" class="form-label">Select File</label>
                        <input type="file" class="form-control" id="file" name="file"
                               accept=".csv,.xlsx,.xls,.parquet,.sas7bdat,.rds,.rdata,.rdat" required>
                        <div class="form-text">
                            Supported formats: CSV, Excel, Parquet, SAS, R data files<br>
                            <strong>Maximum file size: 500MB</strong>
                        </div>
                        <div id="file-info" class="mt-2" style="display: none;">
                            <small class="text-muted">
                                File: <span id="file-name"></span><br>
                                Size: <span id="file-size"></span>
                            </small>
                        </div>
                    </div>

                    <!-- Progress bar (hidden initially) -->
                    <div id="upload-progress" class="mb-3" style="display: none;">
                        <label class="form-label">Upload Progress</label>
                        <div class="progress">
                            <div class="progress-bar progress-bar-striped progress-bar-animated"
                                 role="progressbar" style="width: 0%" id="progress-bar">
                                <span id="progress-text">0%</span>
                            </div>
                        </div>
                        <small class="text-muted mt-1" id="progress-status">Preparing upload...</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="cancel-btn">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="upload-btn">
                        <span id="upload-btn-text">Upload</span>
                        <span id="upload-spinner" class="spinner-border spinner-border-sm ms-2" style="display: none;"></span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the table <strong id="deleteTableName"></strong>?</p>
                <p class="text-danger">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(tableName) {
    document.getElementById('deleteTableName').textContent = tableName;
    document.getElementById('deleteForm').action = '/delete_table/' + tableName;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

// File upload handling
document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('file');
    const fileInfo = document.getElementById('file-info');
    const fileName = document.getElementById('file-name');
    const fileSize = document.getElementById('file-size');
    const uploadForm = document.querySelector('#uploadModal form');
    const uploadProgress = document.getElementById('upload-progress');
    const progressBar = document.getElementById('progress-bar');
    const progressText = document.getElementById('progress-text');
    const progressStatus = document.getElementById('progress-status');
    const uploadBtn = document.getElementById('upload-btn');
    const uploadBtnText = document.getElementById('upload-btn-text');
    const uploadSpinner = document.getElementById('upload-spinner');
    const cancelBtn = document.getElementById('cancel-btn');

    // File size limit (500MB)
    const MAX_FILE_SIZE = 500 * 1024 * 1024;

    // Handle file selection
    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const fileSizeMB = (file.size / (1024 * 1024)).toFixed(2);

            // Show file info
            fileName.textContent = file.name;
            fileSize.textContent = `${fileSizeMB} MB`;
            fileInfo.style.display = 'block';

            // Check file size
            if (file.size > MAX_FILE_SIZE) {
                fileSize.innerHTML = `<span class="text-danger">${fileSizeMB} MB (Too large! Max: 500MB)</span>`;
                uploadBtn.disabled = true;
            } else {
                uploadBtn.disabled = false;

                // Show warning for large files
                if (file.size > 50 * 1024 * 1024) { // 50MB
                    fileSize.innerHTML = `<span class="text-warning">${fileSizeMB} MB (Large file - may take several minutes)</span>`;
                }
            }
        } else {
            fileInfo.style.display = 'none';
            uploadBtn.disabled = false;
        }
    });

    // Handle form submission
    uploadForm.addEventListener('submit', function(e) {
        const file = fileInput.files[0];
        if (file && file.size > MAX_FILE_SIZE) {
            e.preventDefault();
            alert('File is too large. Maximum size is 500MB.');
            return;
        }

        // Show progress for files larger than 10MB
        if (file && file.size > 10 * 1024 * 1024) {
            showUploadProgress();
        }
    });

    function showUploadProgress() {
        // Show progress bar
        uploadProgress.style.display = 'block';

        // Disable form controls
        uploadBtn.disabled = true;
        uploadBtnText.textContent = 'Uploading...';
        uploadSpinner.style.display = 'inline-block';
        cancelBtn.disabled = true;
        fileInput.disabled = true;

        // Simulate progress (since we can't get real upload progress easily)
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress > 90) progress = 90; // Stop at 90% until real completion

            progressBar.style.width = progress + '%';
            progressText.textContent = Math.round(progress) + '%';

            if (progress < 30) {
                progressStatus.textContent = 'Uploading file...';
            } else if (progress < 60) {
                progressStatus.textContent = 'Processing file...';
            } else if (progress < 90) {
                progressStatus.textContent = 'Converting to database format...';
            }
        }, 500);

        // Store interval for cleanup
        uploadForm.progressInterval = interval;
    }

    // Reset form when modal is hidden
    document.getElementById('uploadModal').addEventListener('hidden.bs.modal', function() {
        // Reset form
        uploadForm.reset();
        fileInfo.style.display = 'none';
        uploadProgress.style.display = 'none';

        // Reset button states
        uploadBtn.disabled = false;
        uploadBtnText.textContent = 'Upload';
        uploadSpinner.style.display = 'none';
        cancelBtn.disabled = false;
        fileInput.disabled = false;

        // Clear progress interval
        if (uploadForm.progressInterval) {
            clearInterval(uploadForm.progressInterval);
        }
    });
});
</script>
{% endblock %}
