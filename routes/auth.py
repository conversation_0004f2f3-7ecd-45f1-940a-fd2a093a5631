from flask import Blueprint, render_template, request, redirect, url_for, session, flash, jsonify

auth_bp = Blueprint('auth', __name__, url_prefix='/auth')

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """Login page"""
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        # Simple authentication - replace with proper auth in production
        if username == 'admin' and password == 'admin':
            session['user'] = username
            flash('Login successful!', 'success')
            return redirect(url_for('dashboard.index'))
        else:
            flash('Invalid credentials!', 'error')
    
    return render_template('auth/login.html')

@auth_bp.route('/forgot-password', methods=['GET', 'POST'])
def forgot_password():
    """Forgot password page"""
    if request.method == 'POST':
        email = request.form.get('email')

        # Simple validation - in production, implement proper password reset logic
        if email:
            flash('Password reset instructions have been sent to your email.', 'success')
            return redirect(url_for('auth.login'))
        else:
            flash('Please enter a valid email address.', 'error')

    return render_template('auth/forgot_password.html')

@auth_bp.route('/logout')
def logout():
    """Logout user"""
    session.pop('user', None)
    flash('You have been logged out.', 'info')
    return redirect(url_for('auth.login'))

def login_required(f):
    """Decorator to require login"""
    from functools import wraps
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user' not in session:
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    return decorated_function

@auth_bp.route('/preferences', methods=['GET'])
@login_required
def get_preferences():
    """Get user preferences"""
    default_preferences = {
        'data_formats': ['csv', 'xlsx', 'parquet', 'sas', 'stata', 'rds'],
        'image_formats': ['jpg', 'png'],
        'compression_formats': ['zip', 'tar.gz'],
        'selected_data_format': 'csv',
        'selected_image_format': 'png',
        'selected_compression_format': 'zip'
    }

    # Get preferences from session or use defaults
    preferences = session.get('user_preferences', default_preferences)

    return jsonify({
        'success': True,
        'preferences': preferences
    })

@auth_bp.route('/preferences', methods=['POST'])
@login_required
def save_preferences():
    """Save user preferences"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': 'No preferences data provided'
            }), 400

        # Validate the preferences data
        valid_data_formats = ['csv', 'xlsx', 'parquet', 'sas', 'stata', 'rds']
        valid_image_formats = ['jpg', 'png']
        valid_compression_formats = ['zip', 'tar.gz']

        selected_data_format = data.get('selected_data_format', 'csv')
        selected_image_format = data.get('selected_image_format', 'png')
        selected_compression_format = data.get('selected_compression_format', 'zip')

        if selected_data_format not in valid_data_formats:
            return jsonify({
                'success': False,
                'error': f'Invalid data format: {selected_data_format}'
            }), 400

        if selected_image_format not in valid_image_formats:
            return jsonify({
                'success': False,
                'error': f'Invalid image format: {selected_image_format}'
            }), 400

        if selected_compression_format not in valid_compression_formats:
            return jsonify({
                'success': False,
                'error': f'Invalid compression format: {selected_compression_format}'
            }), 400

        # Save preferences to session
        preferences = {
            'data_formats': valid_data_formats,
            'image_formats': valid_image_formats,
            'compression_formats': valid_compression_formats,
            'selected_data_format': selected_data_format,
            'selected_image_format': selected_image_format,
            'selected_compression_format': selected_compression_format
        }

        session['user_preferences'] = preferences

        return jsonify({
            'success': True,
            'message': 'Preferences saved successfully',
            'preferences': preferences
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'Error saving preferences: {str(e)}'
        }), 500
