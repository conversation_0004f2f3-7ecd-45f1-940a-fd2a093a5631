import duckdb
import pandas as pd
import os
from pathlib import Path
import time
import traceback

class DatabaseManager:
    """Manages DuckDB database operations"""
    
    def __init__(self, db_path='dbs/harmattan.db'):
        self.db_path = db_path
        self.dbs_dir = Path('dbs')
        self.dbs_dir.mkdir(exist_ok=True)
    
    def get_connection(self):
        """Get DuckDB connection"""
        return duckdb.connect(self.db_path)
    
    def list_tables(self):
        """List all tables in the database"""
        try:
            with self.get_connection() as conn:
                # Get parquet files from dbs directory
                parquet_files = list(self.dbs_dir.glob('*.parquet'))
                tables = []
                
                for file_path in parquet_files:
                    table_name = file_path.stem
                    try:
                        # Get basic info about the table
                        result = conn.execute(f"SELECT COUNT(*) as row_count FROM '{file_path}'").fetchone()
                        row_count = result[0] if result else 0
                        
                        # Get file size
                        file_size = file_path.stat().st_size
                        
                        tables.append({
                            'name': table_name,
                            'file_path': str(file_path),
                            'row_count': row_count,
                            'file_size': file_size,
                            'file_size_mb': round(file_size / (1024 * 1024), 2)
                        })
                    except Exception as e:
                        # If there's an error reading the file, still include it but mark as error
                        tables.append({
                            'name': table_name,
                            'file_path': str(file_path),
                            'row_count': 'Error',
                            'file_size': file_path.stat().st_size,
                            'file_size_mb': round(file_path.stat().st_size / (1024 * 1024), 2),
                            'error': str(e)
                        })
                
                return sorted(tables, key=lambda x: x['name'])
        except Exception as e:
            print(f"Error listing tables: {e}")
            return []
    
    def add_table_from_file(self, file_path, table_name):
        """Convert uploaded file to parquet and add to database with memory-efficient processing"""
        try:
            file_ext = Path(file_path).suffix.lower()
            file_size = Path(file_path).stat().st_size

            # For large files (>50MB), use chunked processing
            use_chunked = file_size > 50 * 1024 * 1024  # 50MB threshold

            # Read file based on extension
            if file_ext == '.csv':
                if use_chunked:
                    self._process_csv_chunked(file_path, table_name)
                    return True
                else:
                    df = pd.read_csv(file_path)
            elif file_ext in ['.xlsx', '.xls']:
                # Handle Excel files with multiple sheets
                excel_file = pd.ExcelFile(file_path)
                if len(excel_file.sheet_names) == 1:
                    # Single sheet
                    if use_chunked:
                        # For large Excel files, read in chunks if possible
                        try:
                            df = pd.read_excel(file_path, chunksize=10000)
                            self._process_excel_chunked(df, table_name)
                            return True
                        except:
                            # Fallback to regular reading
                            df = pd.read_excel(file_path)
                    else:
                        df = pd.read_excel(file_path)
                    self._save_as_parquet(df, table_name)
                else:
                    # Multiple sheets - save each as separate table
                    for sheet_name in excel_file.sheet_names:
                        df = pd.read_excel(file_path, sheet_name=sheet_name)
                        sheet_table_name = f"{table_name}_{sheet_name.replace(' ', '_').lower()}"
                        self._save_as_parquet(df, sheet_table_name)
                return True
            elif file_ext == '.parquet':
                df = pd.read_parquet(file_path)
            elif file_ext == '.sas7bdat':
                try:
                    from sas7bdat import SAS7BDAT
                    with SAS7BDAT(file_path) as reader:
                        df = reader.to_data_frame()
                except ImportError:
                    raise Exception("sas7bdat package required for SAS files")
            elif file_ext in ['.rds', '.rdata', '.rdat']:
                try:
                    import rpy2.robjects as robjects
                    from rpy2.robjects import pandas2ri
                    pandas2ri.activate()
                    
                    if file_ext == '.rds':
                        r_data = robjects.r['readRDS'](file_path)
                    else:
                        robjects.r['load'](file_path)
                        # Get the first object from the loaded environment
                        r_data = list(robjects.r.ls())[0]
                        r_data = robjects.r[r_data]
                    
                    df = pandas2ri.rpy2py(r_data)
                except ImportError:
                    raise Exception("rpy2 package required for R files")
            else:
                raise Exception(f"Unsupported file format: {file_ext}")
            
            # Save as parquet if not already handled (Excel case)
            if file_ext != '.xlsx' and file_ext != '.xls':
                self._save_as_parquet(df, table_name)
            
            return True
            
        except Exception as e:
            print(f"Error adding table from file: {e}")
            traceback.print_exc()
            return False
    
    def _process_csv_chunked(self, file_path, table_name):
        """Process large CSV files using DuckDB's efficient CSV reader"""
        parquet_path = self.dbs_dir / f"{table_name}.parquet"

        try:
            with self.get_connection() as conn:
                # Use DuckDB's efficient CSV reader to directly create parquet
                # This avoids loading the entire file into memory
                query = f"""
                COPY (
                    SELECT * FROM read_csv_auto('{file_path}')
                ) TO '{parquet_path}' (FORMAT PARQUET);
                """
                conn.execute(query)

                # Clean up column names in the parquet file
                self._clean_parquet_columns(parquet_path)

        except Exception as e:
            # Fallback to pandas chunked processing if DuckDB fails
            print(f"DuckDB CSV processing failed, falling back to pandas: {e}")
            self._process_csv_pandas_chunked(file_path, table_name)

    def _process_csv_pandas_chunked(self, file_path, table_name):
        """Fallback: Process CSV in pandas chunks (less efficient but more compatible)"""
        chunk_size = 10000
        parquet_path = self.dbs_dir / f"{table_name}.parquet"

        # Use a temporary list to collect chunks, then concat once
        chunks = []
        for chunk in pd.read_csv(file_path, chunksize=chunk_size):
            # Clean column names
            chunk.columns = [col.strip().lower().replace(' ', '_') for col in chunk.columns]
            chunks.append(chunk)

        # Concatenate all chunks at once (more efficient)
        if chunks:
            df = pd.concat(chunks, ignore_index=True)
            df.to_parquet(parquet_path, index=False)

    def _clean_parquet_columns(self, parquet_path):
        """Clean column names in an existing parquet file"""
        try:
            # Read the parquet file
            df = pd.read_parquet(parquet_path)

            # Clean column names
            df.columns = [col.strip().lower().replace(' ', '_') for col in df.columns]

            # Save back to parquet
            df.to_parquet(parquet_path, index=False)

        except Exception as e:
            print(f"Warning: Could not clean column names: {e}")

    def _process_excel_chunked(self, chunk_iterator, table_name):
        """Process Excel files in chunks (if supported)"""
        parquet_path = self.dbs_dir / f"{table_name}.parquet"

        first_chunk = True
        for chunk in chunk_iterator:
            # Clean column names
            chunk.columns = [col.strip().lower().replace(' ', '_') for col in chunk.columns]

            if first_chunk:
                chunk.to_parquet(parquet_path, index=False)
                first_chunk = False
            else:
                existing_df = pd.read_parquet(parquet_path)
                combined_df = pd.concat([existing_df, chunk], ignore_index=True)
                combined_df.to_parquet(parquet_path, index=False)

    def _save_as_parquet(self, df, table_name):
        """Save DataFrame as parquet file"""
        # Clean column names
        df.columns = [col.strip().lower().replace(' ', '_') for col in df.columns]

        # Save as parquet
        parquet_path = self.dbs_dir / f"{table_name}.parquet"
        df.to_parquet(parquet_path, index=False)
    
    def execute_query(self, query):
        """Execute SQL query and return results"""
        start_time = time.time()
        try:
            with self.get_connection() as conn:
                # Register all parquet files as tables
                for parquet_file in self.dbs_dir.glob('*.parquet'):
                    table_name = parquet_file.stem
                    conn.execute(f"CREATE OR REPLACE VIEW {table_name} AS SELECT * FROM '{parquet_file}'")

                # Execute the query
                result = conn.execute(query).fetchall()
                columns = [desc[0] for desc in conn.description] if conn.description else []

                execution_time = round(time.time() - start_time, 3)

                return {
                    'success': True,
                    'data': result,
                    'columns': columns,
                    'row_count': len(result),
                    'execution_time': execution_time
                }

        except Exception as e:
            execution_time = round(time.time() - start_time, 3)
            return {
                'success': False,
                'error': str(e),
                'execution_time': execution_time
            }

    def get_query_count(self, query):
        """Get the count of rows that would be returned by a query"""
        start_time = time.time()
        try:
            with self.get_connection() as conn:
                # Register all parquet files as tables
                for parquet_file in self.dbs_dir.glob('*.parquet'):
                    table_name = parquet_file.stem
                    conn.execute(f"CREATE OR REPLACE VIEW {table_name} AS SELECT * FROM '{parquet_file}'")

                # Wrap the query in a COUNT to get total rows
                count_query = f"SELECT COUNT(*) FROM ({query}) AS subquery"
                count_result = conn.execute(count_query).fetchone()
                total_count = count_result[0] if count_result else 0

                # Also get column information by executing with LIMIT 0
                columns_result = conn.execute(f"{query} LIMIT 0")
                columns = [desc[0] for desc in columns_result.description] if columns_result.description else []

                execution_time = round(time.time() - start_time, 3)

                return {
                    'success': True,
                    'count': total_count,
                    'columns': columns,
                    'execution_time': execution_time
                }

        except Exception as e:
            execution_time = round(time.time() - start_time, 3)
            return {
                'success': False,
                'error': str(e),
                'execution_time': execution_time
            }

    def execute_query_paginated(self, query, page=1, page_size=25):
        """Execute SQL query with pagination"""
        start_time = time.time()
        try:
            with self.get_connection() as conn:
                # Register all parquet files as tables
                for parquet_file in self.dbs_dir.glob('*.parquet'):
                    table_name = parquet_file.stem
                    conn.execute(f"CREATE OR REPLACE VIEW {table_name} AS SELECT * FROM '{parquet_file}'")

                # Get total count first
                count_query = f"SELECT COUNT(*) FROM ({query}) AS subquery"
                count_result = conn.execute(count_query).fetchone()
                total_rows = count_result[0] if count_result else 0

                # Calculate pagination
                offset = (page - 1) * page_size
                total_pages = (total_rows + page_size - 1) // page_size  # Ceiling division

                # Execute paginated query
                paginated_query = f"{query} LIMIT {page_size} OFFSET {offset}"
                result = conn.execute(paginated_query).fetchall()
                columns = [desc[0] for desc in conn.description] if conn.description else []

                execution_time = round(time.time() - start_time, 3)

                return {
                    'success': True,
                    'data': result,
                    'columns': columns,
                    'total_rows': total_rows,
                    'total_pages': total_pages,
                    'current_page': page,
                    'page_size': page_size,
                    'execution_time': execution_time
                }

        except Exception as e:
            execution_time = round(time.time() - start_time, 3)
            return {
                'success': False,
                'error': str(e),
                'execution_time': execution_time
            }
    
    def get_table_info(self, table_name):
        """Get information about a specific table"""
        try:
            parquet_path = self.dbs_dir / f"{table_name}.parquet"
            if not parquet_path.exists():
                return None
            
            with self.get_connection() as conn:
                # Get column information
                result = conn.execute(f"DESCRIBE SELECT * FROM '{parquet_path}'").fetchall()
                columns = [{'name': row[0], 'type': row[1]} for row in result]
                
                # Get row count
                count_result = conn.execute(f"SELECT COUNT(*) FROM '{parquet_path}'").fetchone()
                row_count = count_result[0] if count_result else 0
                
                return {
                    'name': table_name,
                    'columns': columns,
                    'row_count': row_count,
                    'file_size': parquet_path.stat().st_size
                }
                
        except Exception as e:
            print(f"Error getting table info: {e}")
            return None
    
    def get_sample_data(self, table_name, limit=100):
        """Get sample data from a table"""
        try:
            parquet_path = self.dbs_dir / f"{table_name}.parquet"
            if not parquet_path.exists():
                return None
            
            with self.get_connection() as conn:
                result = conn.execute(f"SELECT * FROM '{parquet_path}' LIMIT {limit}").fetchall()
                columns = [desc[0] for desc in conn.description] if conn.description else []
                
                return {
                    'columns': columns,
                    'data': result
                }
                
        except Exception as e:
            print(f"Error getting sample data: {e}")
            return None
    
    def get_table_schema(self, table_name):
        """Get schema information for a table"""
        try:
            parquet_path = self.dbs_dir / f"{table_name}.parquet"
            if not parquet_path.exists():
                return None
            
            with self.get_connection() as conn:
                result = conn.execute(f"DESCRIBE SELECT * FROM '{parquet_path}'").fetchall()
                schema = [{'column': row[0], 'type': row[1]} for row in result]
                
                return schema
                
        except Exception as e:
            print(f"Error getting table schema: {e}")
            return None
    
    def delete_table(self, table_name):
        """Delete a table (remove parquet file)"""
        try:
            parquet_path = self.dbs_dir / f"{table_name}.parquet"
            if parquet_path.exists():
                parquet_path.unlink()
                return True
            return False
        except Exception as e:
            print(f"Error deleting table: {e}")
            return False
