<!doctype html>
<html class="no-js " lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="Responsive Bootstrap 5 admin template and web Application ui kit.">
    <meta name="keyword" content="ALUI, Bootstrap 5, ReactJs, Angular, Laravel, VueJs, ASP .Net, Admin Dashboard, Admin Theme">
    <title>:: ALUI :: Restaurant Admin</title>
    <link rel="icon" href="favicon.ico" type="image/x-icon"> <!-- Favicon-->

    <!-- Plugin css file  -->

    <!-- project css file  -->
    <link rel="stylesheet" href="../../../assets/css/al.style.min.css">
    <!-- project layout css file -->
    <link rel="stylesheet" href="../../../assets/css/layout.d.min.css">
</head>

<body>

<div id="layout-d" class="theme-indigo">

    <!-- sidebar -->
    <div class="sidebar px-3 py-2 py-md-3">
        <div class="d-flex flex-column h-100">
            <h4 class="sidebar-title mb-4 mt-2">AL<span>-UI Admin</span></h4>
            <form class="mb-2 mt-1">
                <div class="input-group">
                    <input type="text" class="form-control border-0" placeholder="Search...">
                </div>
            </form>

            <!-- Menu: main ul -->
            <ul class="menu-list flex-grow-1">
                <li><a class="m-link" href="index.html"><i class="fa fa-dashboard"></i><span>Restaurant Analytics</span></a></li>
                <li><a class="m-link" href="trainers.html"><i class="fa fa-user-circle"></i><span>Trainers</span></a></li>
                <li><a class="m-link" href="workouts.html"><i class="fa fa-pie-chart"></i><span>Workout Builder</span></a></li>
                <li><a class="m-link" href="videos.html"><i class="fa fa-youtube-play"></i><span>Video Tutorial</span></a></li>
                <li class="divider mt-4 py-2 border-top"><small>ACTIVITY</small></li>
                <li><a class="m-link" href="yoga.html"><i class="fa fa-heartbeat"></i><span>Yoga Session</span></a></li>
                <li><a class="m-link" href="sports.html"><i class="fa fa-bolt"></i><span>Sports Activities</span></a></li>
                <li><a class="m-link" href="personal-record.html"><i class="fa fa-black-tie"></i><span>Personal Record</span></a></li>
                <li><a class="m-link" href="diatplan.html"><i class="fa fa-balance-scale"></i><span>Diat plan</span></a></li>
                <li class="divider mt-4 py-2 border-top"><small>DOCUMENTATION</small></li>
                <li><a class="m-link" href="../../documentation/index.html"><i class="fa fa-file-text"></i><span>Documentation</span></a></li>
                <li><a class="m-link" href="../../documentation/changelog.html"><i class="fa fa-pencil"></i><span>Changelog</span> <span id="ALUIversion"></span></a></li>
            </ul>

            <!-- Menu: menu collepce btn -->
            <button type="button" class="btn btn-link sidebar-mini-btn text-light">
                <span><i class="fa fa-arrow-left"></i></span>
            </button>
        </div>
    </div>

    <!-- main body area -->
    <div class="main px-lg-5 px-md-2">

        <!-- Body: Header -->
        <div class="body-header border-bottom d-flex py-lg-3 py-md-2">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col">
                        <ol class="breadcrumb d-inline-flex bg-transparent p-0 m-0">
                            <li class="breadcrumb-item"><a href="index.html">Home</a></li>
                            <li class="breadcrumb-item">Pages</li>
                            <li class="breadcrumb-item active">My Profile</li>
                        </ol>
                        <h1 class="h4 mt-1">My Profile</h1>
                    </div>
                    <div class="col-auto">
                        <button type="button" class="btn btn-white border lift">Add Event</button>
                    </div>
                </div> <!-- Row end  -->

            </div>
        </div>

        <!-- Body: Body -->
        <div class="body d-flex py-lg-3 py-2">
            <div class="container">
                <div class="row">

                    <div class="col-12">

                        <!-- card: Calendar -->
                        <div class="card mb-1">
                            <div class="card-body p-4">
                                <div class="d-flex align-items-center flex-column flex-md-row">
                                    <img src="../../../assets/images/profile_av.png" alt="" class="rounded-circle">
                                    <div class="media-body ms-md-5 m-0 mt-4 mt-md-0 text-md-start text-center">
                                        <h5 class="font-weight-bold">Nellie Maxwell</h5>
                                        <div class="text-muted mb-4"> Lorem ipsum dolor sit amet, nibh suavitate qualisque ut nam. Ad harum primis electram duo, porro principes ei has.</div>
                                        <a href="javascript:void(0)" class="d-inline-block text-primary"> <strong>234</strong> <span class="text-muted">followers</span> </a>
                                        <a href="javascript:void(0)" class="d-inline-block text-primary ms-3"> <strong>111</strong> <span class="text-muted">following</span> </a>
                                    </div>
                                </div>
                            </div>
                        </div> <!-- .Card End -->

                        <!-- Tab: navbar -->
                        <div class="card bg-secondary ps-3 ps-3">
                            <ul class="nav nav-tabs tab-card border-bottom-0 tab-card" role="tablist">
                                <li class="nav-item"><a class="nav-link active" data-bs-toggle="tab" href="#profile-post" role="tab">Post</a></li>
                                <li class="nav-item"><a class="nav-link" data-bs-toggle="tab" href="#profile-groups" role="tab">Groups</a></li>
                                <li class="nav-item"><a class="nav-link" data-bs-toggle="tab" href="#profile-project" role="tab">Projects</a></li>
                                <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle" data-bs-toggle="dropdown" href="#" role="button" aria-expanded="false">Settings</a>
                                    <ul class="dropdown-menu border-0 shadow">
                                        <li><a class="dropdown-item" href="#">Action</a></li>
                                        <li><a class="dropdown-item" href="#">Another action</a></li>
                                        <li><a class="dropdown-item" href="#">Something else here</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="#">Separated link</a></li>
                                    </ul>
                                </li>
                            </ul>
                        </div>

                        <div class="tab-content mt-4 mb-4">
                            <!-- Tab: Post -->
                            <div class="tab-pane fade show active" id="profile-post" role="tabpanel">
                                <div class="row">
                                    <div class="col-lg-8 col-md-12">
                                        <div class="card mb-2">
                                            <div class="card-body">
                                                <div class="post">
                                                    <textarea class="form-control" placeholder="Post" rows="4"></textarea>
                                                    <div class="py-3">
                                                        <a href="#" class="px-3"><i class="fa fa-camera"></i></a>
                                                        <a href="#" class="px-3"><i class="fa fa-video-camera"></i></a>
                                                        <a href="#" class="px-3"><i class="fa fa-music"></i></a>
                                                        <button class="btn btn-outline-primary float-right">Post</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div> <!-- .Card End -->
                                        <ul class="list-unstyled">
                                            <li class="card mb-2 post-card">
                                                <div class="card-body p-lg-5 p-3">
                                                    <div class="d-flex mb-3 pb-3 border-bottom">
                                                        <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar1.jpg" alt="">
                                                        <div class="flex-fill ms-3 text-truncate">
                                                            <h6 class="mb-0"><span class="author">Nellie Maxwell</span> <span class="text-muted small">posted a status</span></h6>
                                                            <span class="text-muted">3 hours ago</span>
                                                        </div>
                                                    </div>
                                                    <div class="timeline-item-post">
                                                        <h6>Hampden-Sydney College in Virginia, looked up one of the more obscure</h6>
                                                        <p>Contrary to popular belief, Lorem Ipsum is not simply random text. It has roots in a piece of classical Latin literature from 45 BC, making it over 2000 years old. Richard McClintock, a Latin professor at Hampden-Sydney College in Virginia, looked up one of the more obscure Latin words, consectetur, from a Lorem Ipsum passage, and going through the cites of the word in classical literature, discovered the undoubtable source.</p>
                                                        <div class="mb-2 mt-4">
                                                            <a class="me-lg-4 me-2 text-primary" href="#"><i class="fa fa-thumbs-up"></i> Like (105)</a>
                                                            <a class="me-lg-4 me-2 text-primary" href="#"><i class="fa fa-comment"></i> Comment (2)</a>
                                                            <a class="me-lg-4 me-2 text-primary" href="#"><i class="fa fa-share"></i> Share (6)</a>
                                                        </div>
                                                        <div>
                                                            <div class="d-flex mt-3 pt-3 border-top">
                                                                <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar2.jpg" alt="">
                                                                <div class="flex-fill ms-3 text-truncate">
                                                                    <p class="mb-0"><span class="author">Rose Rivera</span> <small class="msg-time">1 hour ago</small></p>
                                                                    <span class="text-muted">It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout.</span>
                                                                </div>
                                                            </div>
                                                            <div class="d-flex mt-3 pt-3 border-top">
                                                                <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar3.jpg" alt="">
                                                                <div class="flex-fill ms-3 text-truncate">
                                                                    <p class="mb-0"><span class="author">Robert Hammer</span> <small class="msg-time">1 hour ago</small></p>
                                                                    <span class="text-muted">If you are going to use a passage of Lorem Ipsum, you need to be sure there isn't anything embarrassing</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="mt-4">
                                                        <textarea class="form-control" placeholder="Replay"></textarea>
                                                    </div>
                                                </div>
                                            </li> <!-- .Card End -->
                                            <li class="card mb-2 post-card">
                                                <div class="card-body p-lg-5 p-3">
                                                    <div class="d-flex mb-3 pb-3 border-bottom">
                                                        <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar1.jpg" alt="">
                                                        <div class="flex-fill ms-3 text-truncate">
                                                            <h6 class="mb-0"><span class="author">Nellie Maxwell</span> <span class="text-muted small">posted a status</span></h6>
                                                            <span class="text-muted">5 hours ago</span>
                                                        </div>
                                                    </div>
                                                    <div class="timeline-item-post">
                                                        <h6>Hampden-Sydney College in Virginia, looked up one of the more obscure</h6>
                                                        <p>Contrary to popular belief, Lorem Ipsum is not simply random text.</p>
                                                        <p>Richard McClintock, a Latin professor at Hampden-Sydney College in Virginia, looked up one of the more obscure Latin words, consectetur, from a Lorem Ipsum passage, and going through the cites of the word in classical literature, discovered the undoubtable source.</p>
                                                        <div class="mb-2 mt-4">
                                                            <a class="me-lg-4 me-2 text-primary" href="#"><i class="fa fa-thumbs-up"></i> Like (26)</a>
                                                            <a class="me-lg-4 me-2 text-primary" href="#"><i class="fa fa-comment"></i> Comment (1)</a>
                                                            <a class="me-lg-4 me-2 text-primary" href="#"><i class="fa fa-share"></i> Share (12)</a>
                                                        </div>
                                                        <div>
                                                            <div class="d-flex mt-3 pt-3 border-top">
                                                                <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar2.jpg" alt="">
                                                                <div class="flex-fill ms-3 text-truncate">
                                                                    <p class="mb-0"><span class="author">Rose Rivera</span> <small class="msg-time">1 hour ago</small></p>
                                                                    <span class="text-muted">It is a long established fact that a reader will be distracted by the readable content of a page when looking at its layout.</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="mt-4">
                                                        <textarea class="form-control" placeholder="Replay"></textarea>
                                                    </div>
                                                </div>
                                            </li> <!-- .Card End -->
                                            <li class="card mb-2 post-card">
                                                <div class="card-body p-lg-5 p-3">
                                                    <div class="d-flex mb-3 pb-3 border-bottom">
                                                        <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar1.jpg" alt="">
                                                        <div class="flex-fill ms-3 text-truncate">
                                                            <h6 class="mb-0"><span class="author">Nellie Maxwell</span> <span class="text-muted small">posted a status</span></h6>
                                                            <span class="text-muted">3 hours ago</span>
                                                        </div>
                                                    </div>
                                                    <div class="timeline-item-post">
                                                        <h6>Hampden-Sydney College in Virginia, looked up one of the more obscure</h6>
                                                        <p>Contrary to popular belief, BC, making it over 2000 years old. Richard McClintock, a Latin professor at Hampden-Sydney College in Virginia, looked up one of the more obscure Latin words, consectetur, from a Lorem Ipsum passage, and going through the cites of the word in classical literature, discovered the undoubtable source.</p>
                                                        <div class="mb-2 mt-4">
                                                            <a class="me-lg-4 me-2 text-primary" href="#"><i class="fa fa-thumbs-up"></i> Like (0)</a>
                                                            <a class="me-lg-4 me-2 text-primary" href="#"><i class="fa fa-comment"></i> Comment (0)</a>
                                                            <a class="me-lg-4 me-2 text-primary" href="#"><i class="fa fa-share"></i> Share (0)</a>
                                                        </div>
                                                    </div>
                                                    <div class="mt-4">
                                                        <textarea class="form-control" placeholder="Replay"></textarea>
                                                    </div>
                                                </div>
                                            </li> <!-- .Card End -->
                                        </ul>
                                    </div>
                                    <div class="col-lg-4 col-md-12">
                                        <div class="card mb-2">
                                            <div class="card-body">
                                                <h6 class="card-title mb-3">Categories</h6>
                                                <ul class="list-group list-group-custom">
                                                    <li class="list-group-item"><a class="color-600" href="#">Rent</a></li>
                                                    <li class="list-group-item"><a class="color-600" href="#">Sale</a></li>
                                                    <li class="list-group-item"><a class="color-600" href="#">Top Rating</a></li>
                                                    <li class="list-group-item"><a class="color-600" href="#">Trending</a></li>
                                                    <li class="list-group-item"><a class="color-600" href="#">Newest Properties</a></li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="card mb-2">
                                            <div class="card-body">
                                                <h6 class="card-title mb-3">Latest News</h6>
                                                <ul class="list-group list-group-custom">
                                                    <li class="list-group-item">
                                                        <a href="javascript:void(0);" class="d-flex">
                                                            <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar7.jpg" alt="">
                                                            <div class="flex-fill ms-3">
                                                                <span class="text-primary">There are many variations of passages</span>
                                                                <div><small class="text-muted">10:11 AM</small></div>
                                                            </div>
                                                        </a>
                                                    </li>
                                                    <li class="list-group-item">
                                                        <a href="javascript:void(0);" class="d-flex">
                                                            <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar3.jpg" alt="">
                                                            <div class="flex-fill ms-3">
                                                                <span class="text-primary">Contrary to popular belief, Lorem Ipsum is not simply random text</span>
                                                                <div><small class="text-muted">10:11 AM</small></div>
                                                            </div>
                                                        </a>
                                                    </li>
                                                    <li class="list-group-item">
                                                        <a href="javascript:void(0);" class="d-flex">
                                                            <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar1.jpg" alt="">
                                                            <div class="flex-fill ms-3">
                                                                <span class="text-primary">There are many variations of passages</span>
                                                                <div><small class="text-muted">10:11 AM</small></div>
                                                            </div>
                                                        </a>
                                                    </li>
                                                    <li class="list-group-item">
                                                        <a href="javascript:void(0);" class="d-flex">
                                                            <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar2.jpg" alt="">
                                                            <div class="flex-fill ms-3">
                                                                <span class="text-primary">Contrary to popular belief, Lorem Ipsum is not simply random text</span>
                                                                <div><small class="text-muted">10:11 AM</small></div>
                                                            </div>
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div> <!-- Row end  -->
                            </div>

                            <!-- Tab: Groups -->
                            <div class="tab-pane fade" id="profile-groups" role="tabpanel">
                                <div class="row g-1 row-deck">
                                    <div class="col-xl-2 col-lg-3 col-sm-4 col-6">
                                        <div class="card text-center">
                                            <!-- Group Hover action -->
                                            <div class="btn-group position-absolute top-0 end-0">
                                                <a href="#" class="nav-link py-2 px-3 text-muted" data-bs-toggle="dropdown" aria-expanded="false"><i class="fa fa-ellipsis-v"></i></a>
                                                <ul class="dropdown-menu dropdown-menu-end border-0 shadow">
                                                    <li><a class="dropdown-item" href="#">Edit</a></li>
                                                    <li><a class="dropdown-item" href="#">Share</a></li>
                                                    <li><a class="dropdown-item" href="#">Delete</a></li>
                                                </ul>
                                            </div>                                            
                                            <div class="card-body d-flex align-items-center justify-content-between flex-column">
                                                <div class="me-auto ms-auto py-4">
                                                    <img class="avatar rounded m-1 lift" src="../../../assets/images/xs/avatar1.jpg" data-bs-toggle="tooltip" data-placement="top" title="Avatar Name" alt="">
                                                    <img class="avatar rounded m-1 lift" src="../../../assets/images/xs/avatar2.jpg" data-bs-toggle="tooltip" data-placement="top" title="Avatar Name" alt="">
                                                    <img class="avatar rounded m-1 lift" src="../../../assets/images/xs/avatar3.jpg" data-bs-toggle="tooltip" data-placement="top" title="Avatar Name" alt="">
                                                    <img class="avatar rounded m-1 lift" src="../../../assets/images/xs/avatar4.jpg" data-bs-toggle="tooltip" data-placement="top" title="Avatar Name" alt="">
                                                    <img class="avatar rounded m-1 lift" src="../../../assets/images/xs/avatar5.jpg" data-bs-toggle="tooltip" data-placement="top" title="Avatar Name" alt="">
                                                    <img class="avatar rounded m-1 lift" src="../../../assets/images/xs/avatar6.jpg" data-bs-toggle="tooltip" data-placement="top" title="Avatar Name" alt="">
                                                </div>
                                                <div class="mt-2">
                                                    <h6 class="mb-0">Out Sourcing</h6>
                                                    <small class="text-muted">16 Contacts</small>
                                                </div>
                                            </div>
                                        </div> <!-- .Card End -->
                                    </div>
                                    <div class="col-xl-2 col-lg-3 col-sm-4 col-6">
                                        <div class="card text-center">
                                            <!-- Group Hover action -->
                                            <div class="btn-group position-absolute top-0 end-0">
                                                <a href="#" class="nav-link py-2 px-3 text-muted" data-bs-toggle="dropdown" aria-expanded="false"><i class="fa fa-ellipsis-v"></i></a>
                                                <ul class="dropdown-menu dropdown-menu-end border-0 shadow">
                                                    <li><a class="dropdown-item" href="#">Edit</a></li>
                                                    <li><a class="dropdown-item" href="#">Share</a></li>
                                                    <li><a class="dropdown-item" href="#">Delete</a></li>
                                                </ul>
                                            </div>                                            
                                            <div class="card-body d-flex align-items-center justify-content-between flex-column">
                                                <div class="me-auto ms-auto py-4">
                                                    <img class="avatar rounded m-1 lift" src="../../../assets/images/xs/avatar6.jpg" data-bs-toggle="tooltip" data-placement="top" title="Avatar Name" alt="">
                                                    <img class="avatar rounded m-1 lift" src="../../../assets/images/xs/avatar7.jpg" data-bs-toggle="tooltip" data-placement="top" title="Avatar Name" alt="">
                                                    <img class="avatar rounded m-1 lift" src="../../../assets/images/xs/avatar8.jpg" data-bs-toggle="tooltip" data-placement="top" title="Avatar Name" alt="">
                                                </div>
                                                <div class="mt-2">
                                                    <h6 class="mb-0">Management</h6>
                                                    <small class="text-muted">11 Contacts</small>
                                                </div>
                                            </div>
                                        </div> <!-- .Card End -->
                                    </div>
                                    <div class="col-xl-2 col-lg-3 col-sm-4 col-6">
                                        <div class="card text-center">
                                            <!-- Group Hover action -->
                                            <div class="btn-group position-absolute top-0 end-0">
                                                <a href="#" class="nav-link py-2 px-3 text-muted" data-bs-toggle="dropdown" aria-expanded="false"><i class="fa fa-ellipsis-v"></i></a>
                                                <ul class="dropdown-menu dropdown-menu-end border-0 shadow">
                                                    <li><a class="dropdown-item" href="#">Edit</a></li>
                                                    <li><a class="dropdown-item" href="#">Share</a></li>
                                                    <li><a class="dropdown-item" href="#">Delete</a></li>
                                                </ul>
                                            </div>                                            
                                            <div class="card-body d-flex align-items-center justify-content-between flex-column">
                                                <div class="me-auto ms-auto py-4">
                                                    <img class="avatar rounded m-1 lift" src="../../../assets/images/xs/avatar10.jpg" data-bs-toggle="tooltip" data-placement="top" title="Avatar Name" alt="">
                                                    <img class="avatar rounded m-1 lift" src="../../../assets/images/xs/avatar9.jpg" data-bs-toggle="tooltip" data-placement="top" title="Avatar Name" alt="">
                                                    <img class="avatar rounded m-1 lift" src="../../../assets/images/xs/avatar3.jpg" data-bs-toggle="tooltip" data-placement="top" title="Avatar Name" alt="">
                                                    <img class="avatar rounded m-1 lift" src="../../../assets/images/xs/avatar2.jpg" data-bs-toggle="tooltip" data-placement="top" title="Avatar Name" alt="">
                                                    <img class="avatar rounded m-1 lift" src="../../../assets/images/xs/avatar5.jpg" data-bs-toggle="tooltip" data-placement="top" title="Avatar Name" alt="">
                                                </div>
                                                <div class="mt-2">
                                                    <h6 class="mb-0">Sold Properties</h6>
                                                    <small class="text-muted">106 Contacts</small>
                                                </div>
                                            </div>
                                        </div> <!-- .Card End -->
                                    </div>
                                    <div class="col-xl-2 col-lg-3 col-sm-4 col-6">
                                        <div class="card text-center">
                                            <!-- Group Hover action -->
                                            <div class="btn-group position-absolute top-0 end-0">
                                                <a href="#" class="nav-link py-2 px-3 text-muted" data-bs-toggle="dropdown" aria-expanded="false"><i class="fa fa-ellipsis-v"></i></a>
                                                <ul class="dropdown-menu dropdown-menu-end border-0 shadow">
                                                    <li><a class="dropdown-item" href="#">Edit</a></li>
                                                    <li><a class="dropdown-item" href="#">Share</a></li>
                                                    <li><a class="dropdown-item" href="#">Delete</a></li>
                                                </ul>
                                            </div>                                            
                                            <div class="card-body d-flex align-items-center justify-content-between flex-column">
                                                <div class="me-auto ms-auto py-4">
                                                    <img class="avatar rounded m-1 lift" src="../../../assets/images/xs/avatar1.jpg" data-bs-toggle="tooltip" data-placement="top" title="Avatar Name" alt="">
                                                    <div class="avatar rounded m-1 lift d-inline-flex no-thumbnail">RH</div>
                                                </div>
                                                <div class="mt-2">
                                                    <h6 class="mb-0">San Fransisco</h6>
                                                    <small class="text-muted">126 Contacts</small>
                                                </div>
                                            </div>
                                        </div> <!-- .Card End -->
                                    </div>
                                    <div class="col-xl-2 col-lg-3 col-sm-4 col-6">
                                        <div class="card text-center">
                                            <!-- Group Hover action -->
                                            <div class="btn-group position-absolute top-0 end-0">
                                                <a href="#" class="nav-link py-2 px-3 text-muted" data-bs-toggle="dropdown" aria-expanded="false"><i class="fa fa-ellipsis-v"></i></a>
                                                <ul class="dropdown-menu dropdown-menu-end border-0 shadow">
                                                    <li><a class="dropdown-item" href="#">Edit</a></li>
                                                    <li><a class="dropdown-item" href="#">Share</a></li>
                                                    <li><a class="dropdown-item" href="#">Delete</a></li>
                                                </ul>
                                            </div>                                            
                                            <div class="card-body d-flex align-items-center justify-content-between flex-column">
                                                <div class="me-auto ms-auto py-4">
                                                    <img class="avatar rounded m-1 lift" src="../../../assets/images/xs/avatar6.jpg" data-bs-toggle="tooltip" data-placement="top" title="Avatar Name" alt="">
                                                    <img class="avatar rounded m-1 lift" src="../../../assets/images/xs/avatar7.jpg" data-bs-toggle="tooltip" data-placement="top" title="Avatar Name" alt="">
                                                    <img class="avatar rounded m-1 lift" src="../../../assets/images/xs/avatar8.jpg" data-bs-toggle="tooltip" data-placement="top" title="Avatar Name" alt="">
                                                    <img class="avatar rounded m-1 lift" src="../../../assets/images/xs/avatar1.jpg" data-bs-toggle="tooltip" data-placement="top" title="Avatar Name" alt="">
                                                </div>
                                                <div class="mt-2">
                                                    <h6 class="mb-0">Los Angeles</h6>
                                                    <small class="text-muted">84 Contacts</small>
                                                </div>
                                            </div>
                                        </div> <!-- .Card End -->
                                    </div>
                                    <div class="col-xl-2 col-lg-3 col-sm-4 col-6">
                                        <div class="card text-center">
                                            <!-- Group Hover action -->
                                            <div class="btn-group position-absolute top-0 end-0">
                                                <a href="#" class="nav-link py-2 px-3 text-muted" data-bs-toggle="dropdown" aria-expanded="false"><i class="fa fa-ellipsis-v"></i></a>
                                                <ul class="dropdown-menu dropdown-menu-end border-0 shadow">
                                                    <li><a class="dropdown-item" href="#">Edit</a></li>
                                                    <li><a class="dropdown-item" href="#">Share</a></li>
                                                    <li><a class="dropdown-item" href="#">Delete</a></li>
                                                </ul>
                                            </div>                                            
                                            <div class="card-body d-flex align-items-center justify-content-between flex-column">
                                                <div class="me-auto ms-auto py-4">
                                                    <img class="avatar rounded m-1 lift" src="../../../assets/images/xs/avatar10.jpg" data-bs-toggle="tooltip" data-placement="top" title="Avatar Name" alt="">
                                                    <img class="avatar rounded m-1 lift" src="../../../assets/images/xs/avatar9.jpg" data-bs-toggle="tooltip" data-placement="top" title="Avatar Name" alt="">
                                                    <img class="avatar rounded m-1 lift" src="../../../assets/images/xs/avatar2.jpg" data-bs-toggle="tooltip" data-placement="top" title="Avatar Name" alt="">
                                                    <img class="avatar rounded m-1 lift" src="../../../assets/images/xs/avatar5.jpg" data-bs-toggle="tooltip" data-placement="top" title="Avatar Name" alt="">
                                                </div>
                                                <div class="mt-2">
                                                    <h6 class="mb-0">Colleagues</h6>
                                                    <small class="text-muted">245 Contacts</small>
                                                </div>
                                            </div>
                                        </div> <!-- .Card End -->
                                    </div>
                                    <div class="col-xl-2 col-lg-3 col-sm-4 col-6">
                                        <div class="card text-center">
                                            <!-- Group Hover action -->
                                            <div class="btn-group position-absolute top-0 end-0">
                                                <a href="#" class="nav-link py-2 px-3 text-muted" data-bs-toggle="dropdown" aria-expanded="false"><i class="fa fa-ellipsis-v"></i></a>
                                                <ul class="dropdown-menu dropdown-menu-end border-0 shadow">
                                                    <li><a class="dropdown-item" href="#">Edit</a></li>
                                                    <li><a class="dropdown-item" href="#">Share</a></li>
                                                    <li><a class="dropdown-item" href="#">Delete</a></li>
                                                </ul>
                                            </div>                                            
                                            <div class="card-body d-flex align-items-center justify-content-between flex-column">
                                                <div class="me-auto ms-auto py-4">
                                                    <img class="avatar rounded m-1 lift" src="../../../assets/images/xs/avatar1.jpg" data-bs-toggle="tooltip" data-placement="top" title="Avatar Name" alt="">
                                                    <div class="avatar rounded m-1 lift d-inline-flex no-thumbnail">RH</div>
                                                </div>
                                                <div class="mt-2">
                                                    <h6 class="mb-0">San Fransisco</h6>
                                                    <small class="text-muted">126 Contacts</small>
                                                </div>
                                            </div>
                                        </div> <!-- .Card End -->
                                    </div>
                                    <div class="col-xl-2 col-lg-3 col-sm-4 col-6">
                                        <div class="card text-center">
                                            <!-- Group Hover action -->
                                            <div class="btn-group position-absolute top-0 end-0">
                                                <a href="#" class="nav-link py-2 px-3 text-muted" data-bs-toggle="dropdown" aria-expanded="false"><i class="fa fa-ellipsis-v"></i></a>
                                                <ul class="dropdown-menu dropdown-menu-end border-0 shadow">
                                                    <li><a class="dropdown-item" href="#">Edit</a></li>
                                                    <li><a class="dropdown-item" href="#">Share</a></li>
                                                    <li><a class="dropdown-item" href="#">Delete</a></li>
                                                </ul>
                                            </div>                                            
                                            <div class="card-body d-flex align-items-center justify-content-between flex-column">
                                                <div class="me-auto ms-auto py-4">
                                                    <img class="avatar rounded m-1 lift" src="../../../assets/images/xs/avatar6.jpg" data-bs-toggle="tooltip" data-placement="top" title="Avatar Name" alt="">
                                                    <img class="avatar rounded m-1 lift" src="../../../assets/images/xs/avatar7.jpg" data-bs-toggle="tooltip" data-placement="top" title="Avatar Name" alt="">
                                                    <img class="avatar rounded m-1 lift" src="../../../assets/images/xs/avatar8.jpg" data-bs-toggle="tooltip" data-placement="top" title="Avatar Name" alt="">
                                                    <img class="avatar rounded m-1 lift" src="../../../assets/images/xs/avatar1.jpg" data-bs-toggle="tooltip" data-placement="top" title="Avatar Name" alt="">
                                                </div>
                                                <div class="mt-2">
                                                    <h6 class="mb-0">Los Angeles</h6>
                                                    <small class="text-muted">84 Contacts</small>
                                                </div>
                                            </div>
                                        </div> <!-- .Card End -->
                                    </div>
                                </div> <!-- Row end  -->
                            </div>

                            <!-- Tab: Project -->
                            <div class="tab-pane fade" id="profile-project" role="tabpanel">
                                <div class="row g-1 row-deck">
                                    <div class="col-lg-4 col-md-6 col-sm-12">
                                        <div class="card">
                                            <div class="card-body p-md-4">
                                                <p class="text-muted text-uppercase mb-0 small">Orange Limited</p>
                                                <h5 class="mt-0 mb-3"><a href="#" class="text-primary">New Admin Design</a></h5>
                                                <p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. When an unknown printer took a galley of type and scrambled it...<a href="#" class="font-600 text-muted">view more</a></p>
                                                
                                                <ul class="list-inline">
                                                    <li class="list-inline-item pe-lg-4">
                                                        <h4 class="mb-0">124</h4>
                                                        <p class="text-muted">Attachments</p>
                                                    </li>
                                                    <li class="list-inline-item pe-lg-4">
                                                        <h4 class="mb-0">452</h4>
                                                        <p class="text-muted">Comments</p>
                                                    </li>
                                                    <li class="list-inline-item pe-lg-4">
                                                        <h4 class="mb-0">85</h4>
                                                        <p class="text-muted">Tasks</p>
                                                    </li>
                                                </ul>

                                                <div class="project-members mb-4">
                                                    <label class="me-3">Team :</label>
                                                    <a href="#" title=""><img class="avatar sm rounded" src="../../../assets/images/xs/avatar3.jpg" alt="friend"> </a>
                                                    <a href="#" title=""><img class="avatar sm rounded" src="../../../assets/images/xs/avatar1.jpg" alt="friend"> </a>
                                                    <a href="#" title=""><img class="avatar sm rounded" src="../../../assets/images/xs/avatar7.jpg" alt="friend"> </a>
                                                    <a href="#" title=""><img class="avatar sm rounded" src="../../../assets/images/xs/avatar9.jpg" alt="friend"> </a>
                                                </div>

                                                <label class="small d-flex justify-content-between">Task completed: <span class="text-custom">55/85</span></label>
                                                <div class="progress mt-1" style="height: 7px;">
                                                    <div class="progress-bar bg-info" role="progressbar" aria-valuenow="80" aria-valuemin="0" aria-valuemax="100" style="width: 80%;"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-4 col-md-6 col-sm-12">
                                        <div class="card">
                                            <div class="card-body p-md-4">
                                                <p class="text-muted text-uppercase mb-0 small">Orange Limited</p>
                                                <h5 class="mt-0 mb-3"><a href="#" class="text-primary">OnePage Landing Page HTML</a></h5>
                                                <p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. When an unknown printer took a galley of type and scrambled it...<a href="#" class="font-600 text-muted">view more</a></p>
                                                
                                                <ul class="list-inline">
                                                    <li class="list-inline-item pe-lg-4">
                                                        <h4 class="mb-0">23</h4>
                                                        <p class="text-muted">Attachments</p>
                                                    </li>
                                                    <li class="list-inline-item pe-lg-4">
                                                        <h4 class="mb-0">105</h4>
                                                        <p class="text-muted">Comments</p>
                                                    </li>
                                                    <li class="list-inline-item pe-lg-4">
                                                        <h4 class="mb-0">37</h4>
                                                        <p class="text-muted">Tasks</p>
                                                    </li>
                                                </ul>

                                                <div class="project-members mb-4">
                                                    <label class="me-3">Team :</label>
                                                    <a href="#" title=""><img class="avatar sm rounded" src="../../../assets/images/xs/avatar6.jpg" alt="friend"> </a>
                                                    <a href="#" title=""><img class="avatar sm rounded" src="../../../assets/images/xs/avatar7.jpg" alt="friend"> </a>
                                                </div>

                                                <label class="small d-flex justify-content-between">Task completed: <span class="text-custom">19/37</span></label>
                                                <div class="progress mt-1" style="height: 7px;">
                                                    <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="55" aria-valuemin="0" aria-valuemax="100" style="width: 55%;"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-4 col-md-6 col-sm-12">
                                        <div class="card">
                                            <div class="card-body p-md-4">
                                                <p class="text-muted text-uppercase mb-0 small">Orange Limited</p>
                                                <h5 class="mt-0 mb-3"><a href="#" class="text-primary">New iOS Food App Design</a></h5>
                                                <p>Lorem Ipsum is simply dummy text of the printing and typesetting industry. When an unknown printer took a galley of type and scrambled it...<a href="#" class="font-600 text-muted">view more</a></p>
                                                
                                                <ul class="list-inline">
                                                    <li class="list-inline-item pe-lg-4">
                                                        <h4 class="mb-0">45</h4>
                                                        <p class="text-muted">Attachments</p>
                                                    </li>
                                                    <li class="list-inline-item pe-lg-4">
                                                        <h4 class="mb-0">210</h4>
                                                        <p class="text-muted">Comments</p>
                                                    </li>
                                                    <li class="list-inline-item pe-lg-4">
                                                        <h4 class="mb-0">47</h4>
                                                        <p class="text-muted">Tasks</p>
                                                    </li>
                                                </ul>

                                                <div class="project-members mb-4">
                                                    <label class="me-3">Team :</label>
                                                    <a href="#" title=""><img class="avatar sm rounded" src="../../../assets/images/xs/avatar2.jpg" alt="friend"> </a>
                                                    <a href="#" title=""><img class="avatar sm rounded" src="../../../assets/images/xs/avatar4.jpg" alt="friend"> </a>
                                                    <a href="#" title=""><img class="avatar sm rounded" src="../../../assets/images/xs/avatar9.jpg" alt="friend"> </a>
                                                </div>

                                                <label class="small d-flex justify-content-between">Task completed: <span class="text-custom">12/47</span></label>
                                                <div class="progress mt-1" style="height: 7px;">
                                                    <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="23" aria-valuemin="0" aria-valuemax="100" style="width: 23%;"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div> <!-- Row end  -->
                            </div>
                        </div>

                    </div>

                </div> <!-- Row end  -->
            </div>
        </div>

        <!-- Body: Footer -->
        <div class="body-footer d-flex">

        </div>

    </div>

    <!-- Modal: Layout -->
    <div class="modal fade" id="LayoutModal" tabindex="-1">
        <div class="modal-dialog modal-lg modal-dialog-vertical modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                        <h5 class="modal-title">Ready to build Layouts</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body custom_scroll">
                    <div class="mb-4">Customize your overview page layout. Choose the one that best fits your needs.</div>
                    <h5 class="mt-5 pb-2">Left sidebar with icon</h5>
                    <div class="row g-3">
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-default.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Default</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../index-mini-sidebar.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-mini-sidebar.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Default + Menu Collapse</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-c/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-c.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Menu + Tab view</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-g/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-g.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Icon menu with Grid view</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-i/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-i.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Dual tone icon + menu list</h6>
                                </div>
                            </a>
                        </div>
                    </div>
                    <h5 class="mt-5 pb-2">Header top menu</h5>
                    <div class="row g-3">
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-d/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-d.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header <span class="text-muted small">(Fluid)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift border border-primary bg-primary text-light" href="index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-d-container.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header <span class="text-muted small">(Container)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-d-sub-header/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-d-sub-header.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Sub menu <span class="text-muted small">(Fluid)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-d-sub-header-container/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-d-sub-header-container.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Submenu <span class="text-muted small">(Container)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-f/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-f.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Submenu, Overlay <span class="text-muted small">(Fluid)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-f-container/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-f-container.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Submenu, Overlay <span class="text-muted small">(Container)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-l/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-l.svg" alt="">
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Megamenu + Animation Overlay</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-q/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-q.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Megamenu sticky</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-o/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-o.svg" alt="">
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Full Menu sticky</h6>
                                </div>
                            </a>
                        </div>
                    </div>
                    <h5 class="mt-5 pb-2">Content Combinations</h5>
                    <div class="row g-3">
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-b/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-b.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Default</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-e/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-e.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Default</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-h/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-h.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Default</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-k/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-k.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Body Overlay</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-p/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-p.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Background BG</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-n/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-n.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Sidebar with Tab</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-m/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-m.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0 text-white">Sidebar with Header <span class="small">(Fixed)</span></h6>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal: Setting -->
    <div class="modal fade" id="SettingsModal" tabindex="-1">
        <div class="modal-dialog modal-sm modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">AL-UI Setting</h5>
                </div>
                <div class="modal-body custom_scroll">
                    <!-- Settings: Font -->
                    <div class="setting-font">
                        <small class="card-title text-muted">Google font Settings</small>
                        <ul class="list-group font_setting mb-3 mt-1">
                            <li class="list-group-item py-1 px-2">
                                <div class="form-check mb-0">
                                    <input class="form-check-input" type="radio" name="font" id="font-opensans" value="font-opensans" checked="">
                                    <label class="form-check-label" for="font-opensans">
                                        Open Sans Google Font
                                    </label>
                                </div>
                            </li>
                            <li class="list-group-item py-1 px-2">
                                <div class="form-check mb-0">
                                    <input class="form-check-input" type="radio" name="font" id="font-quicksand" value="font-quicksand">
                                    <label class="form-check-label" for="font-quicksand">
                                        Quicksand Google Font
                                    </label>
                                </div>
                            </li>
                            <li class="list-group-item py-1 px-2">
                                <div class="form-check mb-0">
                                    <input class="form-check-input" type="radio" name="font" id="font-nunito" value="font-nunito">
                                    <label class="form-check-label" for="font-nunito">
                                        Nunito Google Font
                                    </label>
                                </div>
                            </li>
                            <li class="list-group-item py-1 px-2">
                                <div class="form-check mb-0">
                                    <input class="form-check-input" type="radio" name="font" id="font-Raleway" value="font-raleway">
                                    <label class="form-check-label" for="font-Raleway">
                                        Raleway Google Font
                                    </label>
                                </div>
                            </li>
                        </ul>
                    </div>
                    <!-- Settings: Color -->
                    <div class="setting-theme">
                        <small class="card-title text-muted">Theme Color Settings</small>
                        <ul class="list-unstyled d-flex justify-content-between choose-skin mb-2 mt-1">
                            <li data-theme="indigo" class="active"><div class="indigo"></div></li>
                            <li data-theme="blue"><div class="blue"></div></li>
                            <li data-theme="cyan"><div class="cyan"></div></li>
                            <li data-theme="green"><div class="green"></div></li>
                            <li data-theme="orange"><div class="orange"></div></li>
                            <li data-theme="blush"><div class="blush"></div></li>
                            <li data-theme="red"><div class="red"></div></li>
                            <li data-theme="dynamic"><div class="dynamic"><i class="fa fa-paint-brush"></i></div></li>
                        </ul>
                        <div class="form-check form-switch gradient-switch mb-1">
                                <input class="form-check-input" type="checkbox" id="CheckGradient">
                                <label class="form-check-label" for="CheckGradient">Enable Gradient! ( Sidebar )</label>
                            </div>
                    </div>
                    <!-- Settings: bg image -->
                    <div class="setting-img mb-3">
                        <div class="form-check form-switch imagebg-switch mb-1">
                            <input class="form-check-input" type="checkbox" id="CheckImage">
                            <label class="form-check-label" for="CheckImage">Set Background Image (Sidebar)</label>
                        </div>
                        <div class="bg-images">
                            <ul class="list-unstyled d-flex justify-content-between">
                                <li class="sidebar-img-1 sidebar-img-active"><a class="rounded sidebar-img" id="img-1" href="#"><img src="../../assets/images/sidebar-bg/sidebar-1.jpg" alt="" /></a></li>
                                <li class="sidebar-img-2"><a class="rounded sidebar-img" id="img-2" href="#"><img src="../../assets/images/sidebar-bg/sidebar-2.jpg" alt="" /></a></li>
                                <li class="sidebar-img-3"><a class="rounded sidebar-img" id="img-3" href="#"><img src="../../assets/images/sidebar-bg/sidebar-3.jpg" alt="" /></a></li>
                                <li class="sidebar-img-4"><a class="rounded sidebar-img" id="img-4" href="#"><img src="../../assets/images/sidebar-bg/sidebar-4.jpg" alt="" /></a></li>
                                <li class="sidebar-img-5"><a class="rounded sidebar-img" id="img-5" href="#"><img src="../../assets/images/sidebar-bg/sidebar-5.jpg" alt="" /></a></li>
                            </ul>
                        </div>
                    </div>
                    <!-- Settings: Theme dynamics -->
                    <div class="dt-setting">
                        <small class="card-title text-muted">Dynamic Color Settings</small>
                        <ul class="list-group list-unstyled mb-3 mt-1">
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label>Primary Color</label>
                                <button id="primaryColorPicker" class="btn bg-primary avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label>Secondary Color</label>
                                <button id="secondaryColorPicker" class="btn bg-secondary avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label class="text-muted small">Chart Color 1</label>
                                <button id="chartColorPicker1" class="btn chart-color1 avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label class="text-muted small">Chart Color 2</label>
                                <button id="chartColorPicker2" class="btn chart-color2 avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label class="text-muted small">Chart Color 3</label>
                                <button id="chartColorPicker3" class="btn chart-color3 avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label class="text-muted small">Chart Color 4</label>
                                <button id="chartColorPicker4" class="btn chart-color4 avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label class="text-muted small">Chart Color 5</label>
                                <button id="chartColorPicker5" class="btn chart-color5 avatar xs border-0 rounded-0"></button>
                            </li>
                        </ul>
                    </div>
                    <!-- Settings: Light/dark -->
                    <div class="setting-mode">
                        <small class="card-title text-muted">Light/Dark & Contrast Layout</small>
                        <ul class="list-group list-unstyled mb-0 mt-1">
                            <li class="list-group-item d-flex align-items-center py-1 px-2">
                                <div class="form-check form-switch theme-switch mb-0">
                                    <input class="form-check-input" type="checkbox" id="theme-switch">
                                    <label class="form-check-label" for="theme-switch">Enable Dark Mode!</label>
                                </div>
                            </li>
                            <li class="list-group-item d-flex align-items-center py-1 px-2">
                                <div class="form-check form-switch theme-high-contrast mb-0">
                                    <input class="form-check-input" type="checkbox" id="theme-high-contrast">
                                    <label class="form-check-label" for="theme-high-contrast">Enable High Contrast</label>
                                </div>
                            </li>
                            <li class="list-group-item d-flex align-items-center py-1 px-2">
                                <div class="form-check form-switch theme-rtl mb-0">
                                    <input class="form-check-input" type="checkbox" id="theme-rtl">
                                    <label class="form-check-label" for="theme-rtl">Enable RTL Mode!</label>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-start text-center">
                    <button type="button" class="btn flex-fill btn-primary lift">Save Changes</button>
                    <button type="button" class="btn flex-fill btn-white border lift" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

</div>

<!-- Jquery Core Js -->
<script src="../../../assets/bundles/libscripts.bundle.js"></script>
<!-- Plugin Js -->

<!-- Jquery Page Js -->
<script src="../../../assets/js/template.js"></script>
</body>
</html>