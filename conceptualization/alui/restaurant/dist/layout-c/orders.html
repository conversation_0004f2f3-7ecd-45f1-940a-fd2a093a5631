<!doctype html>
<html class="no-js " lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="Responsive Bootstrap 5 admin template and web Application ui kit.">
    <meta name="keyword" content="ALUI, Bootstrap 5, ReactJs, Angular, Laravel, VueJs, ASP .Net, Admin Dashboard, Admin Theme">
    <title>:: ALUI :: Restaurant Admin</title>
    <link rel="icon" href="favicon.ico" type="image/x-icon"> <!-- Favicon-->

    <!-- plugin css file  -->
    <link rel="stylesheet" href="../../../assets/css/dataTables.min.css">
    <link rel="stylesheet" href="../../../assets/css/bootstrapdatepicker.min.css"/>
    
    <!-- project css file  -->
    <link rel="stylesheet" href="../../../assets/css/al.style.min.css">

    <!-- project layout css file -->
    <link rel="stylesheet" href="../../../assets/css/layout.c.min.css">
</head>

<body>

<div id="layout-c" class="theme-indigo">

    <!-- Navigation -->
    <div class="navigation navbar navbar-light justify-content-center px-2 py-2 py-md-3">

        <!-- Brand -->
        <a href="index.html" class="mb-0 mb-lg-3 brand-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="30" viewBox="0 0 64 80" fill="none">
                <path d="M58.8996 22.7L26.9996 2.2C23.4996 -0.0999999 18.9996 0 15.5996 2.5C12.1996 5 10.6996 9.2 11.7996 13.3L15.7996 26.8L3.49962 39.9C-3.30038 47.7 3.79962 54.5 3.89962 54.6L3.99962 54.7L36.3996 78.5C36.4996 78.6 36.5996 78.6 36.6996 78.7C37.8996 79.2 39.1996 79.4 40.3996 79.4C42.9996 79.4 45.4996 78.4 47.4996 76.4C50.2996 73.5 51.1996 69.4 49.6996 65.6L45.1996 51.8L58.9996 39.4C61.7996 37.5 63.3996 34.4 63.3996 31.1C63.4996 27.7 61.7996 24.5 58.8996 22.7ZM46.7996 66.7V66.8C48.0996 69.9 46.8996 72.7 45.2996 74.3C43.7996 75.9 41.0996 77.1 37.9996 76L5.89961 52.3C5.29961 51.7 1.09962 47.3 5.79962 42L16.8996 30.1L23.4996 52.1C24.3996 55.2 26.5996 57.7 29.5996 58.8C30.7996 59.2 31.9996 59.5 33.1996 59.5C35.0996 59.5 36.9996 58.9 38.6996 57.8C38.7996 57.8 38.7996 57.7 38.8996 57.7L42.7996 54.2L46.7996 66.7ZM57.2996 36.9C57.1996 36.9 57.1996 37 57.0996 37L44.0996 48.7L36.4996 25.5V25.4C35.1996 22.2 32.3996 20 28.9996 19.3C25.5996 18.7 22.1996 19.8 19.8996 22.3L18.2996 24L14.7996 12.3C13.8996 8.9 15.4996 6.2 17.3996 4.8C18.4996 4 19.8996 3.4 21.4996 3.4C22.6996 3.4 23.9996 3.7 25.2996 4.6L57.1996 25.1C59.1996 26.4 60.2996 28.6 60.2996 30.9C60.3996 33.4 59.2996 35.6 57.2996 36.9Z" fill="black"/>
            </svg>
        </a>

        <!-- Menu: icon -->
        <ul class="nav navbar-nav flex-row flex-sm-column flex-grow-1 justify-content-start py-2 py-lg-0">

            <!-- Create group -->
            <li class="nav-item"><a class="nav-link p-2 p-lg-3 d-block d-xl-none menu-toggle me-2 me-lg-0" href="#"><i class="fa fa-bars"></i></a></li>
            <li class="nav-item"><a class="nav-link p-2 p-lg-3" href="#" title="Search" data-bs-toggle="modal" data-bs-target="#SearchModal"><i class="fa fa-search"></i></a></li>
            <li class="nav-item"><a class="nav-link p-2 p-lg-3" href="#" title="notifications" data-bs-toggle="modal" data-bs-target="#notificationsModal"><i class="fa fa-bell-o"></i></a></li>
            <li class="nav-item"><a class="nav-link p-2 p-lg-3" href="#" data-bs-toggle="modal" data-bs-target="#LayoutModal"><i class="fa fa-sliders"></i></a>
            <li class="nav-item d-none d-sm-block"><a class="nav-link p-2 p-lg-3" href="chat.html"><i class="fa fa-comments-o"></i></a></li>
            <li class="nav-item d-none d-sm-block flex-grow-1"><a class="nav-link p-2 p-lg-3" href="calendar.html"><i class="fa fa-calendar"></i></a></li>

            <!-- Menu collapse -->
            <li class="nav-item"><a class="nav-link p-2 p-lg-3" href="#" title="Settings" data-bs-toggle="modal" data-bs-target="#SettingsModal"><i class="fa fa-gear"></i></a></li>
            <li class="nav-item d-none d-sm-block"><a class="nav-link p-2 p-lg-3" href="profile.html" title="Menu collapse"><i class="fa fa-user"></i></a></li>

        </ul>

    </div>

    <!-- sidebar -->
    <div class="sidebar my-3 px-4 py-2 py-md-3 rounded bg-dark">
        <div class="d-flex flex-column h-100">
            <h4 class="sidebar-title mb-2 mt-2">AL-UI Admin</h4>
            <form class="mb-3 mt-2">
                <div class="input-group">
                    <input type="text" class="form-control form-control-sm border-0" placeholder="Search...">
                </div>
            </form>

            <!-- Menu: tab -->
            <nav>
                <div class="nav nav-tabs tab-custom p-1 justify-content-between text-center rounded" role="tablist">
                    <a class="flex-fill py-1 px-2 rounded border-0 nav-link active" id="nav-menu-tab" data-bs-toggle="tab" href="#nav-menu" role="tab">Menu</a>
                    <a class="flex-fill py-1 px-2 rounded border-0 nav-link" id="nav-profile-tab" data-bs-toggle="tab" href="#nav-profile" role="tab">Profile</a>
                    <a class="flex-fill py-1 px-2 rounded border-0 nav-link" id="nav-contact-tab" data-bs-toggle="tab" href="#nav-contact" role="tab">Contact</a>
                </div>
            </nav>
            <!-- Menu: tab content -->
            <div class="tab-content flex-grow-1 mt-1">
                <div class="tab-pane fade show active" id="nav-menu">
                    <!-- Menu: main ul -->
                    <ul class="menu-list">
                        <li><a class="m-link" href="index.html"><i class="fa fa-dashboard"></i><span>Dashboard</span></a></li>
                        <li><a class="m-link active" href="orders.html"><i class="fa fa-cart-plus"></i><span>Orders</span></a></li>
                        <li><a class="m-link" href="menu.html"><i class="fa fa-list"></i><span>Menu</span></a></li>
                        <li><a class="m-link" href="employees.html"><i class="fa fa-users"></i><span>Employees</span></a></li>
                        <li><a class="m-link" href="review.html"><i class="fa fa-star"></i><span>Review</span></a></li>
                        <li class="divider mt-4 py-2 border-top"><small>PLUGINS</small></li>
                        <li><a class="m-link" href="#"><i class="fa fa-tags"></i><span>Tomato</span></a></li>
                        <li><a class="m-link" href="#"><i class="fa fa-tags"></i><span>Hber Eats</span></a></li>
                        <li><a class="m-link" href="#"><i class="fa fa-plus"></i><span>Add New</span></a></li>
                        <li class="divider mt-4 py-2 border-top"><small>DOCUMENTATION</small></li>
                        <li><a class="m-link" href="../../../documentation/index.html"><i class="fa fa-file-text"></i><span>Documentation</span></a></li>
                <li><a class="m-link" href="../changelog.html"><i class="fa fa-pencil"></i><span>Changelog</span> <span id="ALUIversion"></span></a></li>
                    </ul>
                    <div class="card">
                        <div class="card-body">
                            <div class="position-relative mb-2">
                                <p class="mb-1 text-uppercase text-muted">Storage</p>
                                <h6 class="position-absolute top-0 end-0">35GB</h6>
                                <div class="progress" style="height: 1px;">
                                    <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="89" aria-valuemin="0" aria-valuemax="100" style="width: 89%;"></div>
                                </div>
                            </div>
                            <button type="button" class="btn btn-primary btn-block lift">
                                <span>Upgrade Now</span>
                                <span class="ms-2"><i class="fa fa-arrow-right"></i></span>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="tab-pane fade" id="nav-profile">
                    <ul class="list-group custom text-light mt-1">
                        <li class="list-group-item px-0 bg-transparent border-0">
                            <a href="#"><img src="../../../assets/images/profile_av.png" alt="User" class="rounded w120"></a>
                            <h5 class="mb-0 mt-3">Robert Hammer</h5>
                            <small class="text-muted">Crossfit Trainer</small>
                        </li>
                        <li class="list-group-item px-0 bg-transparent border-0">
                            <a class="p-1 me-2 text-secondary" href="javascript:void(0);" title="facebook"><i class="fa fa-facebook"></i></a>
                            <a class="p-1 me-2 text-secondary" href="javascript:void(0);" title="twitter"><i class="fa fa-twitter"></i></a>
                            <a class="p-1 me-2 text-secondary" href="javascript:void(0);" title="instagram"><i class="fa fa-instagram"></i></a>
                        </li>
                        <li class="list-group-item px-0 bg-transparent border-0">795 Folsom Ave, Suite 600 San Francisco, CADGE 94107</li>
                        <li class="list-group-item px-0 bg-transparent border-0">
                            <div class="row g-2">
                                <div class="col">
                                    <h5 class="mb-0">852</h5>
                                    <small class="text-muted">Following</small>
                                </div>
                                <div class="col">
                                    <h5 class="mb-0">13k</h5>
                                    <small class="text-muted">Followers</small>
                                </div>
                                <div class="col">
                                    <h5 class="mb-0">234</h5>
                                    <small class="text-muted">Post</small>
                                </div>
                            </div>
                        </li>
                        <li class="list-group-item px-0 bg-transparent border-0">
                            <small class="text-muted">Email address: </small>
                            <p class="mb-0"><EMAIL></p>
                        </li>
                        <li class="list-group-item px-0 bg-transparent border-0">
                            <small class="text-muted">Phone: </small>
                            <p class="mb-0">+ ************</p>
                        </li>
                        <li class="list-group-item px-0 bg-transparent border-0 mt-3">
                            <div class="mb-3">
                                <div class="mb-1 text-muted">Photoshop</div>
                                <div class="progress" style="height: 3px;">
                                    <div class="progress-bar bg-secondary" role="progressbar" aria-valuenow="89" aria-valuemin="0" aria-valuemax="100" style="width: 89%"></div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="mb-1 text-muted">Wordpress</div>
                                <div class="progress" style="height: 3px;">
                                    <div class="progress-bar bg-primary" role="progressbar" aria-valuenow="56" aria-valuemin="0" aria-valuemax="100" style="width: 56%"></div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="mb-1 text-muted">HTML 5</div>
                                <div class="progress" style="height: 3px;">
                                    <div class="progress-bar bg-success" role="progressbar" aria-valuenow="78" aria-valuemin="0" aria-valuemax="100" style="width: 78%"></div>
                                </div>
                            </div>
                            <div class="mb-0">
                                <div class="mb-1 text-muted">Angular</div>
                                <div class="progress" style="height: 3px;">
                                    <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="43" aria-valuemin="0" aria-valuemax="100" style="width: 43%"></div>
                                </div>
                            </div>
                        </li>
                        <li class="list-group-item px-0 bg-transparent border-0">
                            <button type="button" class="btn btn-primary btn-block lift">Update Profile</button>
                        </li>
                    </ul>
                </div>
                <div class="tab-pane fade" id="nav-contact">
                    <ul class="list-group list-group-flush mb-0">
                        <li class="list-group-item px-2 py-3 bg-transparent d-flex">
                            <img class="avatar sm rounded-circle" src="../../../assets/images/xs/avatar1.jpg" alt="">
                            <div class="flex-fill d-flex justify-content-between align-items-center ms-2">
                                <a href="#" class="text-light">Chris Fox</a>
                                <small>
                                    <a class="px-1 text-secondary" href="#"><i class="fa fa-envelope"></i></a>
                                    <a class="px-1 text-danger" href="#"><i class="fa fa-trash"></i></a>
                                </small>
                            </div>
                        </li>
                        <li class="list-group-item px-2 py-3 bg-transparent d-flex">
                            <img class="avatar sm rounded-circle" src="../../../assets/images/xs/avatar2.jpg" alt="">
                            <div class="flex-fill d-flex justify-content-between align-items-center ms-2">
                                <a href="#" class="text-light">Robert Hammer</a>
                                <small>
                                    <a class="px-1 text-secondary" href="#"><i class="fa fa-envelope"></i></a>
                                    <a class="px-1 text-danger" href="#"><i class="fa fa-trash"></i></a>
                                </small>
                            </div>
                        </li>
                        <li class="list-group-item px-2 py-3 bg-transparent d-flex">
                            <img class="avatar sm rounded-circle" src="../../../assets/images/xs/avatar3.jpg" alt="">
                            <div class="flex-fill d-flex justify-content-between align-items-center ms-2">
                                <a href="#" class="text-light">Airi Satou</a>
                                <small>
                                    <a class="px-1 text-secondary" href="#"><i class="fa fa-envelope"></i></a>
                                    <a class="px-1 text-danger" href="#"><i class="fa fa-trash"></i></a>
                                </small>
                            </div>
                        </li>
                        <li class="list-group-item px-2 py-3 bg-transparent d-flex">
                            <img class="avatar sm rounded-circle" src="../../../assets/images/xs/avatar4.jpg" alt="">
                            <div class="flex-fill d-flex justify-content-between align-items-center ms-2">
                                <a href="#" class="text-light">Bruno Nash</a>
                                <small>
                                    <a class="px-1 text-secondary" href="#"><i class="fa fa-envelope"></i></a>
                                    <a class="px-1 text-danger" href="#"><i class="fa fa-trash"></i></a>
                                </small>
                            </div>
                        </li>
                        <li class="list-group-item px-2 py-3 bg-transparent d-flex">
                            <img class="avatar sm rounded-circle" src="../../../assets/images/xs/avatar5.jpg" alt="">
                            <div class="flex-fill d-flex justify-content-between align-items-center ms-2">
                                <a href="#" class="text-light">Michael Silva</a>
                                <small>
                                    <a class="px-1 text-secondary" href="#"><i class="fa fa-envelope"></i></a>
                                    <a class="px-1 text-danger" href="#"><i class="fa fa-trash"></i></a>
                                </small>
                            </div>
                        </li>
                        <li class="list-group-item px-2 py-3 bg-transparent d-flex">
                            <img class="avatar sm rounded-circle" src="../../../assets/images/xs/avatar6.jpg" alt="">
                            <div class="flex-fill d-flex justify-content-between align-items-center ms-2">
                                <a href="#" class="text-light">Vivian Harrell</a>
                                <small>
                                    <a class="px-1 text-secondary" href="#"><i class="fa fa-envelope"></i></a>
                                    <a class="px-1 text-danger" href="#"><i class="fa fa-trash"></i></a>
                                </small>
                            </div>
                        </li>
                        <li class="list-group-item px-2 py-3 bg-transparent d-flex">
                            <img class="avatar sm rounded-circle" src="../../../assets/images/xs/avatar7.jpg" alt="">
                            <div class="flex-fill d-flex justify-content-between align-items-center ms-2">
                                <a href="#" class="text-light">Chris Fox</a>
                                <small>
                                    <a class="px-1 text-secondary" href="#"><i class="fa fa-envelope"></i></a>
                                    <a class="px-1 text-danger" href="#"><i class="fa fa-trash"></i></a>
                                </small>
                            </div>
                        </li>
                        <li class="list-group-item px-2 py-3 bg-transparent d-flex">
                            <img class="avatar sm rounded-circle" src="../../../assets/images/xs/avatar8.jpg" alt="">
                            <div class="flex-fill d-flex justify-content-between align-items-center ms-2">
                                <a href="#" class="text-light">Robert Hammer</a>
                                <small>
                                    <a class="px-1 text-secondary" href="#"><i class="fa fa-envelope"></i></a>
                                    <a class="px-1 text-danger" href="#"><i class="fa fa-trash"></i></a>
                                </small>
                            </div>
                        </li>
                        <li class="list-group-item px-2 py-3 bg-transparent d-flex">
                            <img class="avatar sm rounded-circle" src="../../../assets/images/xs/avatar9.jpg" alt="">
                            <div class="flex-fill d-flex justify-content-between align-items-center ms-2">
                                <a href="#" class="text-light">Airi Satou</a>
                                <small>
                                    <a class="px-1 text-secondary" href="#"><i class="fa fa-envelope"></i></a>
                                    <a class="px-1 text-danger" href="#"><i class="fa fa-trash"></i></a>
                                </small>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>

        </div>
    </div>

    <!-- main body area -->
    <div class="main">
        
        <!-- Body: Body -->
        <div class="body d-flex">
            <div class="container-fluid px-0">

                <div class="row g-0">
                    <div class="col-12 d-flex food-order">
                        <div class="card bg-transparent border-0 order-0 w-100 vh100 list">
                            <div class="card-body p-lg-4">
                                
                                <!-- Body: Header -->
                                <div class="body-header border-bottom pb-2">
                                    <div class="row align-items-center">
                                        <div class="col">
                                            <!-- Pretitle -->
                                            <small class="text-muted">Welcome to ALUI</small>
                                            <h1 class="h4 mt-1">Order List</h1>
                                        </div>
                                    </div> <!-- Row end  -->
                                </div>

                                <div class="card-body px-0">
                                    <div class="row">
                                        <div class="col-xl-2 col-lg-3 col-md-6 col-sm-6">
                                            <label class="form-label">Select Month</label>
                                            <select class="form-control">
                                                <option>Select</option>
                                                <option>January</option>
                                                <option>February</option>
                                                <option>March</option>
                                                <option>April</option>
                                                <option>May</option>
                                                <option>June</option>
                                                <option>July</option>
                                                <option>August</option>
                                                <option>September</option>
                                                <option>October</option>
                                                <option>November</option>
                                                <option>December</option>
                                            </select>
                                        </div>
                                        <div class="col-xl-4 col-lg-4 col-md-6 col-sm-6">
                                            <label class="form-label">Search...</label>
                                            <input type="text" class="form-control" placeholder="Search...">
                                        </div>
                                        <div class="col-xl-6 col-lg-5 col-md-12 text-lg-end">
                                            <label class="form-label">Action</label>
                                            <div>
                                                <button type="button" class="btn btn-sm btn-primary d-inline-block d-sm-none btn-detail"><i class="fa fa-area-chart"></i></button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary"><i class="fa fa-envelope"></i></button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary"><i class="fa fa-download"></i></button>
                                                <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#add-workout">Create New</button>
                                            </div>
                                        </div>
                                    </div> <!-- Row end  -->
                                </div>

                                <table class="table table-hover myDataTable table-bordered align-middle mb-0 custom-table">
                                    <thead>
                                        <tr>                                       
                                            <th>Order ID:</th>
                                            <th>Customers</th>
                                            <th>Address</th>
                                            <th>Menu</th>
                                            <th>Status</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>LA-0211</td>
                                            <td>
                                                <a href="javascript:void(0);" class="d-flex link-secondary">
                                                    <img class="avatar sm rounded" src="../../../assets/images/xs/avatar1.jpg" alt="">
                                                    <div class="h6 mb-0 ms-2">Maryam Amiri</div>
                                                </a>
                                            </td>
                                            <td>
                                                <div>44 Shirley Ave. West Chicago, IL 60185</div>
                                                <span class="text-muted">3km</span>
                                            </td>
                                            <td>
                                                <div>Fresh salad bowl (2)</div>
                                                <span class="text-muted">$80.5</span>
                                            </td>
                                            <td><span class="badge bg-success">Completed</span></td>
                                            <td>
                                                <button type="button" class="btn btn-link btn-sm text-muted" data-bs-toggle="tooltip" data-bs-placement="top" title="Send Reports"><i class="fa fa-envelope"></i></button>
                                                <button type="button" class="btn btn-link btn-sm text-muted" data-bs-toggle="tooltip" data-bs-placement="top" title="Download"><i class="fa fa-download"></i></button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>LA-0212</td>
                                            <td>
                                                <a href="javascript:void(0);" class="d-flex link-secondary">
                                                    <img class="avatar sm rounded" src="../../../assets/images/xs/avatar2.jpg" alt="">
                                                    <div class="h6 mb-0 ms-2">jonathan hope</div>
                                                </a>
                                            </td>
                                            <td>
                                                <div>123 6th St. Melbourne, FL 32904</div>
                                                <span class="text-muted">4km</span>
                                            </td>
                                            <td>
                                                <div>Fresh salad bowl (2)</div>
                                                <span class="text-muted">$80.5</span>
                                            </td>
                                            <td><span class="badge bg-success">Completed</span></td>
                                            <td>
                                                <button type="button" class="btn btn-link btn-sm text-muted" data-bs-toggle="tooltip" data-bs-placement="top" title="Send Reports"><i class="fa fa-envelope"></i></button>
                                                <button type="button" class="btn btn-link btn-sm text-muted" data-bs-toggle="tooltip" data-bs-placement="top" title="Download"><i class="fa fa-download"></i></button>
                                            </td>
                                        </tr>
                                        <tr class="table-secondary">
                                            <td>LA-0213</td>
                                            <td>
                                                <a href="javascript:void(0);" class="d-flex link-secondary">
                                                    <img class="avatar sm rounded" src="../../../assets/images/xs/avatar3.jpg" alt="">
                                                    <div class="h6 mb-0 ms-2">Francisco Vasquez</div>
                                                </a>
                                            </td>
                                            <td>
                                                <div>44 Shirley Ave. West Chicago, IL 60185</div>
                                                <span class="text-muted">3km</span>
                                            </td>
                                            <td>
                                                <div>Fresh salad bowl (2)</div>
                                                <span class="text-muted">$80.5</span>
                                            </td>
                                            <td><span class="badge bg-success">Completed</span></td>
                                            <td>
                                                <button type="button" class="btn btn-link btn-sm text-muted" data-bs-toggle="tooltip" data-bs-placement="top" title="Send Reports"><i class="fa fa-envelope"></i></button>
                                                <button type="button" class="btn btn-link btn-sm text-muted" data-bs-toggle="tooltip" data-bs-placement="top" title="Download"><i class="fa fa-download"></i></button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>LA-0215</td>
                                            <td>
                                                <a href="javascript:void(0);" class="d-flex link-secondary">
                                                    <img class="avatar sm rounded" src="../../../assets/images/xs/avatar4.jpg" alt="">
                                                    <div class="h6 mb-0 ms-2">Maryam Amiri</div>
                                                </a>
                                            </td>
                                            <td>
                                                <div>514 S. Magnolia St. Orlando, FL 32806</div>
                                                <span class="text-muted">2.7km</span>
                                            </td>
                                            <td>
                                                <div>Fresh salad bowl (2)</div>
                                                <span class="text-muted">$80.5</span>
                                            </td>
                                            <td><span class="badge bg-info">Delivering</span></td>
                                            <td>
                                                <button type="button" class="btn btn-link btn-sm text-muted" data-bs-toggle="tooltip" data-bs-placement="top" title="Send Reports"><i class="fa fa-envelope"></i></button>
                                                <button type="button" class="btn btn-link btn-sm text-muted" data-bs-toggle="tooltip" data-bs-placement="top" title="Download"><i class="fa fa-download"></i></button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>LA-0217</td>
                                            <td>
                                                <a href="javascript:void(0);" class="d-flex link-secondary">
                                                    <img class="avatar sm rounded" src="../../../assets/images/xs/avatar5.jpg" alt="">
                                                    <div class="h6 mb-0 ms-2">Debra Stewart</div>
                                                </a>
                                            </td>
                                            <td>
                                                <div>44 Shirley Ave. West Chicago, IL 60185</div>
                                                <span class="text-muted">3km</span>
                                            </td>
                                            <td>
                                                <div>Cheese Garlic Bread (10)</div>
                                                <span class="text-muted">$257</span>
                                            </td>
                                            <td><span class="badge bg-warning">Preparing</span></td>
                                            <td>
                                                <button type="button" class="btn btn-link btn-sm text-muted" data-bs-toggle="tooltip" data-bs-placement="top" title="Send Reports"><i class="fa fa-envelope"></i></button>
                                                <button type="button" class="btn btn-link btn-sm text-muted" data-bs-toggle="tooltip" data-bs-placement="top" title="Download"><i class="fa fa-download"></i></button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>LA-0220</td>
                                            <td>
                                                <a href="javascript:void(0);" class="d-flex link-secondary">
                                                    <img class="avatar sm rounded" src="../../../assets/images/xs/avatar6.jpg" alt="">
                                                    <div class="h6 mb-0 ms-2">Jane Hunt</div>
                                                </a>
                                            </td>
                                            <td>
                                                <div>25 6th St. Melbourne, FL 32904</div>
                                                <span class="text-muted">6km</span>
                                            </td>
                                            <td>
                                                <div>Fresh salad bowl (2)</div>
                                                <span class="text-muted">$80.5</span>
                                            </td>
                                            <td><span class="badge bg-danger">Cencal</span></td>
                                            <td>
                                                <button type="button" class="btn btn-link btn-sm text-muted" data-bs-toggle="tooltip" data-bs-placement="top" title="Send Reports"><i class="fa fa-envelope"></i></button>
                                                <button type="button" class="btn btn-link btn-sm text-muted" data-bs-toggle="tooltip" data-bs-placement="top" title="Download"><i class="fa fa-download"></i></button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>LA-0212</td>
                                            <td>
                                                <a href="javascript:void(0);" class="d-flex link-secondary">
                                                    <img class="avatar sm rounded" src="../../../assets/images/xs/avatar7.jpg" alt="">
                                                    <div class="h6 mb-0 ms-2">Francisco Vasquez</div>
                                                </a>
                                            </td>
                                            <td>
                                                <div>23 Shirley Ave. West Chicago, IL 60185</div>
                                                <span class="text-muted">3km</span>
                                            </td>
                                            <td>
                                                <div>Crispy Calamari (3)</div>
                                                <span class="text-muted">$180.5</span>
                                            </td>
                                            <td><span class="badge bg-warning">Preparing</span></td>
                                            <td>
                                                <button type="button" class="btn btn-link btn-sm text-muted" data-bs-toggle="tooltip" data-bs-placement="top" title="Send Reports"><i class="fa fa-envelope"></i></button>
                                                <button type="button" class="btn btn-link btn-sm text-muted" data-bs-toggle="tooltip" data-bs-placement="top" title="Download"><i class="fa fa-download"></i></button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>LA-0213</td>
                                            <td>
                                                <a href="javascript:void(0);" class="d-flex link-secondary">
                                                    <img class="avatar sm rounded" src="../../../assets/images/xs/avatar8.jpg" alt="">
                                                    <div class="h6 mb-0 ms-2">Susie Willis</div>
                                                </a>
                                            </td>
                                            <td>
                                                <div>73 Bowman St. South Windsor, CT 06074</div>
                                                <span class="text-muted">3km</span>
                                            </td>
                                            <td>
                                                <div>Fresh salad bowl (2)</div>
                                                <span class="text-muted">$80.5</span>
                                            </td>
                                            <td><span class="badge bg-warning">Preparing</span></td>
                                            <td>
                                                <button type="button" class="btn btn-link btn-sm text-muted" data-bs-toggle="tooltip" data-bs-placement="top" title="Send Reports"><i class="fa fa-envelope"></i></button>
                                                <button type="button" class="btn btn-link btn-sm text-muted" data-bs-toggle="tooltip" data-bs-placement="top" title="Download"><i class="fa fa-download"></i></button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>LA-0215</td>
                                            <td>
                                                <a href="javascript:void(0);" class="d-flex link-secondary">
                                                    <img class="avatar sm rounded" src="../../../assets/images/xs/avatar9.jpg" alt="">
                                                    <div class="h6 mb-0 ms-2">Debra Stewart</div>
                                                </a>
                                            </td>
                                            <td>
                                                <div>11 Shirley Ave. West Chicago, IL 60185</div>
                                                <span class="text-muted">2.9km</span>
                                            </td>
                                            <td>
                                                <div>Fresh salad bowl (2)</div>
                                                <span class="text-muted">$80.5</span>
                                            </td>
                                            <td><span class="badge bg-info">Delivering</span></td>
                                            <td>
                                                <button type="button" class="btn btn-link btn-sm text-muted" data-bs-toggle="tooltip" data-bs-placement="top" title="Send Reports"><i class="fa fa-envelope"></i></button>
                                                <button type="button" class="btn btn-link btn-sm text-muted" data-bs-toggle="tooltip" data-bs-placement="top" title="Download"><i class="fa fa-download"></i></button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="card border-0 order-1 details">
                            <div class="card-body p-lg-4">
                                <ul class="nav nav-tabs tab-body-header rounded d-flex mt-2 text-center" role="tablist">
                                    <li class="nav-item flex-fill"><a class="nav-link active" data-bs-toggle="tab" href="#nav-Items" role="tab">Items</a></li>
                                    <li class="nav-item flex-fill"><a class="nav-link" data-bs-toggle="tab" href="#nav-Progress" role="tab">Progress</a></li>
                                    <li class="nav-item flex-fill"><a class="nav-link" data-bs-toggle="tab" href="#nav-Reviews" role="tab">Reviews</a></li>
                                </ul>
                                
                                <div class="tab-content mt-2">
                                    <div class="tab-pane fade show active" id="nav-Items" role="tabpanel">
                                        <ul class="list-unstyled list-group list-group-custom list-group-flush mb-0">
                                            <li class="list-group-item py-3 px-0">
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar lg no-thumbnail bg-transparent">
                                                        <img class="img-fluid rounded" src="../../../assets/images/fitness/food-1.jpg" alt="" />    
                                                    </div>
                                                    <div class="flex-fill ms-3 text-truncate">
                                                        <h6 class="mb-0">Fresh salad bowl (2)</h6>
                                                        <span class="text-muted">Extra Cheese</span>
                                                    </div>
                                                </div>
                                            </li>
                                            <li class="list-group-item py-3 px-0">
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar lg no-thumbnail bg-transparent">
                                                        <img class="img-fluid rounded" src="../../../assets/images/fitness/food-2.jpg" alt="" />    
                                                    </div>
                                                    <div class="flex-fill ms-3 text-truncate">
                                                        <h6 class="mb-0">Cheese Garlic Bread (10)</h6>
                                                        <span class="text-muted">Extra Cheese</span>
                                                    </div>
                                                </div>
                                            </li>
                                            <li class="list-group-item py-3 px-0">
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar lg no-thumbnail bg-transparent">
                                                        <img class="img-fluid rounded" src="../../../assets/images/fitness/food-3.jpg" alt="" />    
                                                    </div>
                                                    <div class="flex-fill ms-3 text-truncate">
                                                        <h6 class="mb-0">Fresh salad bowl (3)</h6>
                                                        <span class="text-muted">Extra Cheese</span>
                                                    </div>
                                                </div>
                                            </li>
                                            <li class="list-group-item py-3 px-0">
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar lg no-thumbnail bg-transparent">
                                                        <img class="img-fluid rounded" src="../../../assets/images/fitness/food-4.jpg" alt="" />    
                                                    </div>
                                                    <div class="flex-fill ms-3 text-truncate">
                                                        <h6 class="mb-0">Fresh salad bowl (2)</h6>
                                                        <span class="text-muted">Extra Cheese</span>
                                                    </div>
                                                </div>
                                            </li>
                                            <li class="list-group-item py-3 px-0">
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar lg no-thumbnail bg-transparent">
                                                        <img class="img-fluid rounded" src="../../../assets/images/fitness/food-1.jpg" alt="" />    
                                                    </div>
                                                    <div class="flex-fill ms-3 text-truncate">
                                                        <h6 class="mb-0">Fresh salad bowl (2)</h6>
                                                        <span class="text-muted">Extra Cheese</span>
                                                    </div>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="tab-pane fade" id="nav-Progress" role="tabpanel">
                                        <div class="custom_scroll" style="height: 380px; scroll-behavior: smooth;">
                                            <ul class="timeline-activity list-unstyled mb-0">
                                                <li class="activity px-3 py-2 mb-1" data-date="12:30 - Sun">
                                                    <div class="fw-bold small d-flex justify-content-between align-items-center">New Order <span class="badge bg-warning">ID: 215</span></div>
                                                    <div>
                                                        <small class="text-muted">1 Burger, 1 Corn Rice curd</small>
                                                    </div>
                                                </li>
                                                <li class="activity px-3 py-2 mb-1" data-date="12:31 - Sun">
                                                    <div class="fw-bold small">Order Received</div>
                                                    <div>
                                                        
                                                    </div>
                                                </li>
                                                <li class="activity px-3 py-2 mb-1" data-date="12:32 - Sun">
                                                    <div class="fw-bold small">Payment Verify</div>
                                                    <div>
                                                        <h5 class="mb-0 text-success">$80.5 - Done</h5>
                                                        <small class="text-muted">NetBanking</small>
                                                    </div>
                                                </li>
                                                <li class="activity px-3 py-2 mb-1" data-date="12:35 - Sun">
                                                    <div class="fw-bold small">Order inprogress</div>
                                                    <div>
                                                        <label class="me-2">Team:</label>
                                                        <a href="#" title="avatar"><img class="avatar xs rounded" src="../../../assets/images/xs/avatar3.jpg" alt="friend"> </a>
                                                        <a href="#" title="avatar"><img class="avatar xs rounded" src="../../../assets/images/xs/avatar1.jpg" alt="friend"> </a>
                                                        <a href="#" title="avatar"><img class="avatar xs rounded" src="../../../assets/images/xs/avatar7.jpg" alt="friend"> </a>
                                                    </div>
                                                </li>
                                                <li class="activity px-3 py-2 mb-1" data-date="12:55 - Sun">
                                                    <div class="fw-bold small">Delivery on the way</div>
                                                    <div>
                                                        <p class="mb-1 small text-muted"><i class="fa fa-map-marker ps-1"></i> 123 6th St. Melbourne, FL 32904</p>
                                                        <a href="#" title="avatar"><img class="avatar xs rounded" src="../../../assets/images/xs/avatar5.jpg" alt="friend"> </a>
                                                        <label class="ms-1">Robert Hammer</label>
                                                    </div>
                                                </li>
                                                <li class="activity px-3 py-2 mb-1" data-date="01:10 - Sun">
                                                    <div class="fw-bold small d-flex justify-content-between align-items-center">Delivery<span class="badge bg-success">Done</span></div>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade" id="nav-Reviews" role="tabpanel">
                                        ....
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>

        <!-- Body: Footer -->
        <div class="body-footer d-flex">
        </div>

    </div>

    <!-- Modal: notifications -->
    <div class="modal fade" id="notificationsModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-vertical modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header px-md-5">
                    <h5 class="modal-title">Notifications <span class="badge bg-danger ms-2">14</span></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="bg-light border-bottom px-2 px-md-5 py-3">
                        <ul class="nav nav-pills nav-fill" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" data-bs-toggle="tab" href="#Noti-tab-Message" role="tab"
                                    aria-selected="true">Message</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="tab" href="#Noti-tab-Events" role="tab"
                                    aria-selected="false">Events</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="tab" href="#Noti-tab-Logs" role="tab">Logs</a>
                            </li>
                        </ul>
                    </div>
                    <div class="modal-body px-md-5 custom_scroll">
                    <div class="tab-content p-0">
                        <div class="tab-pane fade active show" id="Noti-tab-Message" role="tabpanel">
                            <ul class="list-unstyled list mb-0">
                                <li class="py-4 border-bottom">
                                    <a href="javascript:void(0);" class="d-flex">
                                        <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar1.jpg" alt="">
                                        <div class="flex-fill ms-3">
                                            <p class="d-flex justify-content-between mb-0 text-muted"><span>Chris Fox</span> <small>2MIN</small></p>
                                            <span class="text-muted">changed an issue from "In Progress" to <span class="badge bg-success">Review</span></span>
                                        </div>
                                    </a>
                                </li>
                                <li class="py-4 border-bottom">
                                    <a href="javascript:void(0);" class="d-flex">
                                        <div class="avatar rounded-circle no-thumbnail">RH</div>
                                        <div class="flex-fill ms-3">
                                            <p class="d-flex justify-content-between mb-0 text-muted"><span>Robert Hammer</span> <small>13MIN</small></p>
                                            <span class="text-muted">It is a long established fact that a reader will be distracted</span>
                                        </div>
                                    </a>
                                </li>
                                <li class="py-4 border-bottom">
                                    <a href="javascript:void(0);" class="d-flex">
                                        <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar3.jpg" alt="">
                                        <div class="flex-fill ms-3">
                                            <p class="d-flex justify-content-between mb-0 text-muted"><span>Orlando Lentz</span> <small>1HR</small></p>
                                            <span class="text-muted">There are many variations of passages</span>
                                        </div>
                                    </a>
                                </li>
                                <li class="py-4 border-bottom">
                                    <a href="javascript:void(0);" class="d-flex">
                                        <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar4.jpg" alt="">
                                        <div class="flex-fill ms-3">
                                            <p class="d-flex justify-content-between mb-0 text-muted"><span>Barbara Kelly</span> <small>1DAY</small></p>
                                            <span class="text-muted">Contrary to popular belief <span class="badge bg-danger">Code</span></span>
                                        </div>
                                    </a>
                                </li>
                                <li class="py-4 border-bottom">
                                    <a href="javascript:void(0);" class="d-flex">
                                        <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar5.jpg" alt="">
                                        <div class="flex-fill ms-3">
                                            <p class="d-flex justify-content-between mb-0 text-muted"><span>Robert Hammer</span> <small>13MIN</small></p>
                                            <span class="text-muted">making it over 2000 years old</span>
                                        </div>
                                    </a>
                                </li>
                                <li class="py-4 border-bottom">
                                    <a href="javascript:void(0);" class="d-flex">
                                        <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar6.jpg" alt="">
                                        <div class="flex-fill ms-3">
                                            <p class="d-flex justify-content-between mb-0 text-muted"><span>Orlando Lentz</span> <small>1HR</small></p>
                                            <span class="text-muted">There are many variations of passages</span>
                                        </div>
                                    </a>
                                </li>
                                <li class="py-4">
                                    <a href="javascript:void(0);" class="d-flex">
                                        <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar7.jpg" alt="">
                                        <div class="flex-fill ms-3">
                                            <p class="d-flex justify-content-between mb-0 text-muted"><span>Rose Rivera</span> <small class="">1DAY</small></p>
                                            <span class="text-muted">The generated Lorem Ipsum</span>
                                        </div>
                                    </a>
                                </li>
                            </ul>
                        </div>
                        <div class="tab-pane fade" id="Noti-tab-Events" role="tabpanel">
                            <ul class="list-unstyled list mb-0">
                                <li class="py-4 border-bottom">
                                    <a href="javascript:void(0);" class="d-flex">
                                        <div class="avatar rounded no-thumbnail"><i class="fa fa-info-circle fa-lg"></i></div>
                                        <div class="flex-fill ms-3">
                                            <p class="mb-0 text-muted">Campaign <strong class="text-primary">Holiday Sale</strong> is nearly reach budget limit.</p>
                                            <small class="text-muted">10:00 AM Today</small>
                                        </div>
                                    </a>
                                </li>
                                <li class="py-4 border-bottom">
                                    <a href="javascript:void(0);" class="d-flex">
                                        <div class="avatar rounded no-thumbnail"><i class="fa fa-thumbs-up fa-lg"></i></div>
                                        <div class="flex-fill ms-3">
                                            <p class="mb-0 text-muted">Your New Campaign <strong class="text-primary">Holiday Sale</strong> is approved.</p>
                                            <small class="text-muted">11:30 AM Today</small>
                                        </div>
                                    </a>
                                </li>
                                <li class="py-4 border-bottom">
                                    <a href="javascript:void(0);" class="d-flex">
                                        <div class="avatar rounded no-thumbnail"><i class="fa fa-pie-chart fa-lg"></i></div>
                                        <div class="flex-fill ms-3">
                                            <p class="mb-0 text-muted">Website visits from Twitter is <strong class="text-danger">27% higher</strong> than last week.</p>
                                            <small class="text-muted">04:00 PM Today</small>
                                        </div>
                                    </a>
                                </li>
                                <li class="py-4 border-bottom">
                                    <a href="javascript:void(0);" class="d-flex">
                                        <div class="avatar rounded no-thumbnail"><i class="fa fa-warning fa-lg"></i></div>
                                        <div class="flex-fill ms-3">
                                            <p class="mb-0 text-muted"><strong class="text-warning">Error</strong> on website analytics configurations</p>
                                            <small class="text-muted">Yesterday</small>
                                        </div>
                                    </a>
                                </li>
                                <li class="py-4">
                                    <a href="javascript:void(0);" class="d-flex">
                                        <div class="avatar rounded no-thumbnail"><i class="fa fa-thumbs-up fa-lg"></i></div>
                                        <div class="flex-fill ms-3">
                                            <p class="mb-0 text-muted">Your New Campaign <strong class="text-primary">Holiday Sale</strong> is approved.</p>
                                            <small class="text-muted">11:30 AM Today</small>
                                        </div>
                                    </a>
                                </li>
                            </ul>
                        </div>
                        <div class="tab-pane fade" id="Noti-tab-Logs" role="tabpanel">
                            <h4>No Logs right now!</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal: Search -->
    <div class="modal fade" id="SearchModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-vertical modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header bg-secondary border-bottom-0 px-3 px-md-5">
                    <h5 class="modal-title" >Search</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body custom_scroll">
                    <div class="card-body-height py-4 px-2 px-md-4">
                        <form class="mb-3">
                            <div class="input-group mb-3">
                                <input type="text" class="form-control" placeholder="Search...">
                                <button class="btn btn-outline-secondary" type="button"><span class="fa fa-search"></span> Search</button>
                            </div>
                        </form>

                        <small class="dropdown-header">Recent searches</small>              
                        <div class="dropdown-item bg-transparent text-wrap my-2">
                            <span class="h4 me-1">
                                <a class="btn btn-sm btn-dark" href="#">Github <i class="fa fa-search ms-1"></i></a>
                            </span>
                            <span class="h4">
                                <a class="btn btn-sm btn-dark" href="#">Notification panel <i class="fa fa-search ms-1"></i></a>
                            </span>
                            <span class="h4">
                                <a class="btn btn-sm btn-dark" href="#">New project <i class="fa fa-search ms-1"></i></a>
                            </span>
                        </div>
            
                        <div class="dropdown-divider my-3"></div>
                        
                        <small class="dropdown-header">Tutorials</small>              
                        <a class="dropdown-item py-2" href="#">
                            <div class="d-flex align-items-center">
                                <span class="avatar sm no-thumbnail me-2"><i class="fa fa-github"></i></span>
                                <div class="text-truncate">
                                    <span>How to set up Github?</span>
                                </div>
                            </div>
                        </a>              
                        <a class="dropdown-item py-2" href="#">
                            <div class="d-flex align-items-center">
                                <span class="avatar sm no-thumbnail me-2"><i class="fa fa-paint-brush"></i></span>
                                <div class="text-truncate">
                                    <span>How to change theme color?</span>
                                </div>
                            </div>
                        </a>
            
                        <div class="dropdown-divider my-3"></div>
            
                        <small class="dropdown-header">Members</small>              
                        <a class="dropdown-item py-2" href="#">
                            <div class="d-flex align-items-center">
                                <img class="avatar sm rounded-circle" src="../../../assets/images/xs/avatar1.jpg" alt="">
                                <div class="text-truncate ms-2">
                                    <span>Robert Hammer <i class="fa fa-check-circle text-primary" data-bs-toggle="tooltip" data-placement="top" title="" data-original-title="Top endorsed"></i></span>
                                </div>
                            </div>
                        </a>              
                        <a class="dropdown-item py-2" href="#">
                            <div class="d-flex align-items-center">
                                <img class="avatar sm rounded-circle" src="../../../assets/images/xs/avatar2.jpg" alt="">
                                <div class="text-truncate ms-2">
                                    <span>Orlando Lentz</span>
                                </div>
                            </div>
                        </a>              
                        <a class="dropdown-item py-2" href="#">
                            <div class="d-flex align-items-center">
                                <div class="avatar sm rounded-circle no-thumbnail">RH</div>
                                <div class="text-truncate ms-2">
                                    <span>Brian Swader</span>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal: Layout -->
    <div class="modal fade" id="LayoutModal" tabindex="-1">
        <div class="modal-dialog modal-lg modal-dialog-vertical modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                        <h5 class="modal-title">Ready to build Layouts</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body custom_scroll">
                    <div class="mb-4">Customize your overview page layout. Choose the one that best fits your needs.</div>
                    <h5 class="mt-5 pb-2">Left sidebar with icon</h5>
                    <div class="row g-3">
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-default.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Default</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../index-mini-sidebar.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-mini-sidebar.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Default + Menu Collapse</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift border border-primary bg-primary text-light" href="index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-c.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Menu + Tab view</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-g/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-g.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Icon menu with Grid view</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-i/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-i.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Dual tone icon + menu list</h6>
                                </div>
                            </a>
                        </div>
                    </div>
                    <h5 class="mt-5 pb-2">Header top menu</h5>
                    <div class="row g-3">
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="layout-d/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-d.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header <span class="text-muted small">(Fluid)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="layout-d-container/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-d-container.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header <span class="text-muted small">(Container)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="layout-d-sub-header/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-d-sub-header.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Sub menu <span class="text-muted small">(Fluid)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="layout-d-sub-header-container/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-d-sub-header-container.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Submenu <span class="text-muted small">(Container)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="layout-f/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-f.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Submenu, Overlay <span class="text-muted small">(Fluid)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="layout-f-container/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-f-container.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Submenu, Overlay <span class="text-muted small">(Container)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-l/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-l.svg" alt="">
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Megamenu + Animation Overlay</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-q/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-q.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Megamenu sticky</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-o/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-o.svg" alt="">
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Full Menu sticky</h6>
                                </div>
                            </a>
                        </div>
                    </div>
                    <h5 class="mt-5 pb-2">Content Combinations</h5>
                    <div class="row g-3">
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="layout-b/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-b.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Default</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="layout-e/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-e.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Default</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="layout-h/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-h.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Default</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="layout-n/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-n.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Sidebar with Tab</h6>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal: Setting -->
    <div class="modal fade" id="SettingsModal" tabindex="-1">
        <div class="modal-dialog modal-sm modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">AL-UI Setting</h5>
                </div>
                <div class="modal-body custom_scroll">
                    <!-- Settings: Font -->
                    <div class="setting-font">
                        <small class="card-title text-muted">Google font Settings</small>
                        <ul class="list-group font_setting mb-3 mt-1">
                            <li class="list-group-item py-1 px-2">
                                <div class="form-check mb-0">
                                    <input class="form-check-input" type="radio" name="font" id="font-opensans" value="font-opensans" checked="">
                                    <label class="form-check-label" for="font-opensans">
                                        Open Sans Google Font
                                    </label>
                                </div>
                            </li>
                            <li class="list-group-item py-1 px-2">
                                <div class="form-check mb-0">
                                    <input class="form-check-input" type="radio" name="font" id="font-quicksand" value="font-quicksand">
                                    <label class="form-check-label" for="font-quicksand">
                                        Quicksand Google Font
                                    </label>
                                </div>
                            </li>
                            <li class="list-group-item py-1 px-2">
                                <div class="form-check mb-0">
                                    <input class="form-check-input" type="radio" name="font" id="font-nunito" value="font-nunito">
                                    <label class="form-check-label" for="font-nunito">
                                        Nunito Google Font
                                    </label>
                                </div>
                            </li>
                            <li class="list-group-item py-1 px-2">
                                <div class="form-check mb-0">
                                    <input class="form-check-input" type="radio" name="font" id="font-Raleway" value="font-raleway">
                                    <label class="form-check-label" for="font-Raleway">
                                        Raleway Google Font
                                    </label>
                                </div>
                            </li>
                        </ul>
                    </div>
                    <!-- Settings: Color -->
                    <div class="setting-theme">
                        <small class="card-title text-muted">Theme Color Settings</small>
                        <ul class="list-unstyled d-flex justify-content-between choose-skin mb-2 mt-1">
                            <li data-theme="indigo" class="active"><div class="indigo"></div></li>
                            <li data-theme="blue"><div class="blue"></div></li>
                            <li data-theme="cyan"><div class="cyan"></div></li>
                            <li data-theme="green"><div class="green"></div></li>
                            <li data-theme="orange"><div class="orange"></div></li>
                            <li data-theme="blush"><div class="blush"></div></li>
                            <li data-theme="red"><div class="red"></div></li>
                            <li data-theme="dynamic"><div class="dynamic"><i class="fa fa-paint-brush"></i></div></li>
                        </ul>
                        <div class="form-check form-switch gradient-switch mb-1">
                                <input class="form-check-input" type="checkbox" id="CheckGradient">
                                <label class="form-check-label" for="CheckGradient">Enable Gradient! ( Sidebar )</label>
                            </div>
                    </div>
                    <!-- Settings: bg image -->
                    <div class="setting-img mb-3">
                        <div class="form-check form-switch imagebg-switch mb-1">
                            <input class="form-check-input" type="checkbox" id="CheckImage">
                            <label class="form-check-label" for="CheckImage">Set Background Image (Sidebar)</label>
                        </div>
                        <div class="bg-images">
                            <ul class="list-unstyled d-flex justify-content-between">
                                <li class="sidebar-img-1 sidebar-img-active"><a class="rounded sidebar-img" id="img-1" href="#"><img src="../../assets/images/sidebar-bg/sidebar-1.jpg" alt="" /></a></li>
                                <li class="sidebar-img-2"><a class="rounded sidebar-img" id="img-2" href="#"><img src="../../assets/images/sidebar-bg/sidebar-2.jpg" alt="" /></a></li>
                                <li class="sidebar-img-3"><a class="rounded sidebar-img" id="img-3" href="#"><img src="../../assets/images/sidebar-bg/sidebar-3.jpg" alt="" /></a></li>
                                <li class="sidebar-img-4"><a class="rounded sidebar-img" id="img-4" href="#"><img src="../../assets/images/sidebar-bg/sidebar-4.jpg" alt="" /></a></li>
                                <li class="sidebar-img-5"><a class="rounded sidebar-img" id="img-5" href="#"><img src="../../assets/images/sidebar-bg/sidebar-5.jpg" alt="" /></a></li>
                            </ul>
                        </div>
                    </div>
                    <!-- Settings: Theme dynamics -->
                    <div class="dt-setting">
                        <small class="card-title text-muted">Dynamic Color Settings</small>
                        <ul class="list-group list-unstyled mb-3 mt-1">
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label>Primary Color</label>
                                <button id="primaryColorPicker" class="btn bg-primary avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label>Secondary Color</label>
                                <button id="secondaryColorPicker" class="btn bg-secondary avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label class="text-muted small">Chart Color 1</label>
                                <button id="chartColorPicker1" class="btn chart-color1 avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label class="text-muted small">Chart Color 2</label>
                                <button id="chartColorPicker2" class="btn chart-color2 avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label class="text-muted small">Chart Color 3</label>
                                <button id="chartColorPicker3" class="btn chart-color3 avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label class="text-muted small">Chart Color 4</label>
                                <button id="chartColorPicker4" class="btn chart-color4 avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label class="text-muted small">Chart Color 5</label>
                                <button id="chartColorPicker5" class="btn chart-color5 avatar xs border-0 rounded-0"></button>
                            </li>
                        </ul>
                    </div>
                    <!-- Settings: Light/dark -->
                    <div class="setting-mode">
                        <small class="card-title text-muted">Light/Dark & Contrast Layout</small>
                        <ul class="list-group list-unstyled mb-0 mt-1">
                            <li class="list-group-item d-flex align-items-center py-1 px-2">
                                <div class="form-check form-switch theme-switch mb-0">
                                    <input class="form-check-input" type="checkbox" id="theme-switch">
                                    <label class="form-check-label" for="theme-switch">Enable Dark Mode!</label>
                                </div>
                            </li>
                            <li class="list-group-item d-flex align-items-center py-1 px-2">
                                <div class="form-check form-switch theme-high-contrast mb-0">
                                    <input class="form-check-input" type="checkbox" id="theme-high-contrast">
                                    <label class="form-check-label" for="theme-high-contrast">Enable High Contrast</label>
                                </div>
                            </li>
                            <li class="list-group-item d-flex align-items-center py-1 px-2">
                                <div class="form-check form-switch theme-rtl mb-0">
                                    <input class="form-check-input" type="checkbox" id="theme-rtl">
                                    <label class="form-check-label" for="theme-rtl">Enable RTL Mode!</label>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-start text-center">
                    <button type="button" class="btn flex-fill btn-primary lift">Save Changes</button>
                    <button type="button" class="btn flex-fill btn-white border lift" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

</div>

<!-- Jquery Core Js -->
<script src="../../../assets/bundles/libscripts.bundle.js"></script>

<!-- Plugin Js -->
<script src="../../../assets/bundles/dataTables.bundle.js"></script>

<!-- Jquery Page Js -->
<script src="../../../assets/js/template.js"></script>
<script>

    // project data table
    $('.myDataTable').addClass( 'nowrap' );
    $('.myDataTable') .dataTable({
        responsive: true,
        searching: false,
        paging: false,
        ordering: false,
        info: false,
        columnDefs: [{ targets: [-1, -3], className: 'dt-body-right' }]
    });

    $('.btn-detail').on('click', function () {
        $('.details').toggleClass('open');
    });
</script>
</body>
</html>