<!doctype html>
<html class="no-js " lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="Responsive Bootstrap 5 admin template and web Application ui kit.">
    <meta name="keyword" content="ALUI, Bootstrap 5, ReactJs, Angular, Laravel, VueJs, ASP .Net, Admin Dashboard, Admin Theme">
    <title>:: ALUI :: Restaurant Admin</title>
    <link rel="icon" href="favicon.ico" type="image/x-icon"> <!-- Favicon-->

    <!-- plugin css file  -->
    <link rel="stylesheet" href="../../../assets/css/dataTables.min.css">
    <link rel="stylesheet" href="../../../assets/css/bootstrapdatepicker.min.css"/>
    
    <!-- project css file  -->
    <link rel="stylesheet" href="../../../assets/css/al.style.min.css">

    <!-- project layout css file -->
    <link rel="stylesheet" href="../../../assets/css/layout.d.sub.header.min.css">
</head>

<body>

<div id="layout-d-sub-header" class="theme-indigo">

    <!-- Navigation -->
    <div class="header fixed-top shadow-sm">
        <nav class="navbar navbar-light bg-secondary py-2 py-md-3 px-lg-5 px-md-2">
            <div class="container-fluid">

                <!-- Brand -->
                <a href="index.html" class="me-3 me-lg-4 brand-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" viewBox="0 0 64 80" fill="none">
                        <path d="M58.8996 22.7L26.9996 2.2C23.4996 -0.0999999 18.9996 0 15.5996 2.5C12.1996 5 10.6996 9.2 11.7996 13.3L15.7996 26.8L3.49962 39.9C-3.30038 47.7 3.79962 54.5 3.89962 54.6L3.99962 54.7L36.3996 78.5C36.4996 78.6 36.5996 78.6 36.6996 78.7C37.8996 79.2 39.1996 79.4 40.3996 79.4C42.9996 79.4 45.4996 78.4 47.4996 76.4C50.2996 73.5 51.1996 69.4 49.6996 65.6L45.1996 51.8L58.9996 39.4C61.7996 37.5 63.3996 34.4 63.3996 31.1C63.4996 27.7 61.7996 24.5 58.8996 22.7ZM46.7996 66.7V66.8C48.0996 69.9 46.8996 72.7 45.2996 74.3C43.7996 75.9 41.0996 77.1 37.9996 76L5.89961 52.3C5.29961 51.7 1.09962 47.3 5.79962 42L16.8996 30.1L23.4996 52.1C24.3996 55.2 26.5996 57.7 29.5996 58.8C30.7996 59.2 31.9996 59.5 33.1996 59.5C35.0996 59.5 36.9996 58.9 38.6996 57.8C38.7996 57.8 38.7996 57.7 38.8996 57.7L42.7996 54.2L46.7996 66.7ZM57.2996 36.9C57.1996 36.9 57.1996 37 57.0996 37L44.0996 48.7L36.4996 25.5V25.4C35.1996 22.2 32.3996 20 28.9996 19.3C25.5996 18.7 22.1996 19.8 19.8996 22.3L18.2996 24L14.7996 12.3C13.8996 8.9 15.4996 6.2 17.3996 4.8C18.4996 4 19.8996 3.4 21.4996 3.4C22.6996 3.4 23.9996 3.7 25.2996 4.6L57.1996 25.1C59.1996 26.4 60.2996 28.6 60.2996 30.9C60.3996 33.4 59.2996 35.6 57.2996 36.9Z" fill="black"/>
                    </svg>
                </a>

                <!-- Search -->
                <div class="h-left d-none d-sm-block">
                    <div class="input-group border rounded">
                        <button class="btn btn-outline-secondary dropdown-toggle border-0 d-none d-sm-block" type="button" data-bs-toggle="dropdown" aria-expanded="false">Fillter</button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#">Action</a></li>
                            <li><a class="dropdown-item" href="#">Another action</a></li>
                            <li><a class="dropdown-item" href="#">Something else here</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#">Separated link</a></li>
                        </ul>
                        <input type="text" class="form-control bg-transparent border-0" placeholder="Search here...">
                    </div>
                </div>

                <!-- header rightbar icon -->
                <div class="h-right flex-grow-1 justify-content-end d-flex align-items-center me-5 me-lg-0">
                    <div class="d-flex">
                        <a class="nav-link text-primary" href="#" title="Settings" data-bs-toggle="modal" data-bs-target="#SettingsModal"><i class="fa fa-gear"></i></a>
                        <a class="nav-link text-primary" href="#" data-bs-toggle="modal" data-bs-target="#LayoutModal">
                            <i class="fa fa-sliders"></i>
                        </a>
                    </div>
                    <div class="dropdown notifications">
                        <a class="nav-link dropdown-toggle pulse" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fa fa-bell"></i>
                            <span class="pulse-ring"></span>
                        </a>
                        <div id="NotificationsDiv" class="dropdown-menu rounded-lg shadow border-0 dropdown-animation dropdown-menu-end p-0 m-0">
                            <div class="card border-0 w380">
                                <div class="card-header border-0 p-3">
                                    <h5 class="mb-0 fw-light d-flex justify-content-between">
                                        <span>Notifications Center</span>
                                        <span class="badge text-muted">14</span>
                                    </h5>
                                    <ul class="nav nav-tabs mt-3 border-bottom-0" role="tablist">
                                        <li class="nav-item">
                                            <a class="nav-link fw-light ps-0 me-2 active" data-bs-toggle="tab" href="#Noti-tab-Message" role="tab">Message</a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link fw-light me-2" data-bs-toggle="tab" href="#Noti-tab-Events" role="tab">Events</a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link fw-light" data-bs-toggle="tab" href="#Noti-tab-Logs" role="tab">Logs</a>
                                        </li>
                                    </ul>
                                </div>
                                <div class="tab-content card-body custom_scroll">
                                    <div class="tab-pane fade show active" id="Noti-tab-Message" role="tabpanel">
                                        <ul class="list-unstyled list mb-0">
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar1.jpg" alt="">
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span>Chris Fox</span> <small>2MIN</small></p>
                                                        <span class="text-muted">changed an issue from "In Progress" to <span class="badge bg-success">Review</span></span>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <div class="avatar rounded-circle no-thumbnail">RH</div>
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span>Robert Hammer</span> <small>13MIN</small></p>
                                                        <span class="text-muted">It is a long established fact that a reader will be distracted</span>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar3.jpg" alt="">
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span>Orlando Lentz</span> <small>1HR</small></p>
                                                        <span class="text-muted">There are many variations of passages</span>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar4.jpg" alt="">
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span>Barbara Kelly</span> <small>1DAY</small></p>
                                                        <span class="text-muted">Contrary to popular belief <span class="badge bg-danger">Code</span></span>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar5.jpg" alt="">
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span>Robert Hammer</span> <small>13MIN</small></p>
                                                        <span class="text-muted">making it over 2000 years old</span>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar6.jpg" alt="">
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span>Orlando Lentz</span> <small>1HR</small></p>
                                                        <span class="text-muted">There are many variations of passages</span>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <img class="avatar rounded-circle" src="../../../assets/images/xs/avatar7.jpg" alt="">
                                                    <div class="flex-fill ms-3">
                                                        <p class="d-flex justify-content-between mb-0 text-muted"><span>Rose Rivera</span> <small class="">1DAY</small></p>
                                                        <span class="text-muted">The generated Lorem Ipsum</span>
                                                    </div>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="tab-pane fade" id="Noti-tab-Events" role="tabpanel">
                                        <ul class="list-unstyled list mb-0">
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <div class="avatar rounded no-thumbnail"><i class="fa fa-info-circle fa-lg"></i></div>
                                                    <div class="flex-fill ms-3">
                                                        <p class="mb-0 text-muted">Campaign <strong class="text-primary">Holiday Sale</strong> is nearly reach budget limit.</p>
                                                        <small class="text-muted">10:00 AM Today</small>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <div class="avatar rounded no-thumbnail"><i class="fa fa-thumbs-up fa-lg"></i></div>
                                                    <div class="flex-fill ms-3">
                                                        <p class="mb-0 text-muted">Your New Campaign <strong class="text-primary">Holiday Sale</strong> is approved.</p>
                                                        <small class="text-muted">11:30 AM Today</small>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <div class="avatar rounded no-thumbnail"><i class="fa fa-pie-chart fa-lg"></i></div>
                                                    <div class="flex-fill ms-3">
                                                        <p class="mb-0 text-muted">Website visits from Twitter is <strong class="text-danger">27% higher</strong> than last week.</p>
                                                        <small class="text-muted">04:00 PM Today</small>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <div class="avatar rounded no-thumbnail"><i class="fa fa-warning fa-lg"></i></div>
                                                    <div class="flex-fill ms-3">
                                                        <p class="mb-0 text-muted"><strong class="text-warning">Error</strong> on website analytics configurations</p>
                                                        <small class="text-muted">Yesterday</small>
                                                    </div>
                                                </a>
                                            </li>
                                            <li class="py-2 mb-1 border-bottom">
                                                <a href="javascript:void(0);" class="d-flex">
                                                    <div class="avatar rounded no-thumbnail"><i class="fa fa-thumbs-up fa-lg"></i></div>
                                                    <div class="flex-fill ms-3">
                                                        <p class="mb-0 text-muted">Your New Campaign <strong class="text-primary">Holiday Sale</strong> is approved.</p>
                                                        <small class="text-muted">11:30 AM Today</small>
                                                    </div>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="tab-pane fade" id="Noti-tab-Logs" role="tabpanel">
                                        <h4>No Logs right now!</h4>
                                    </div>
                                </div>
                                <a class="card-footer text-center border-top-0" href="#"> View all notifications</a>
                            </div>
                        </div>
                    </div>
                    <div class="dropdown user-profile ms-2 ms-sm-3">
                        <a class="nav-link dropdown-toggle pulse p-0" href="#" role="button" data-bs-toggle="dropdown">
                            <img class="avatar rounded-circle img-thumbnail" src="../../../assets/images/profile_av.png" alt="">
                        </a>
                        <div class="dropdown-menu rounded-lg shadow border-0 dropdown-animation dropdown-menu-end p-0 m-0">
                            <div class="card border-0 w240">
                                <div class="card-body border-bottom">
                                    <div class="d-flex py-1">
                                        <img class="avatar rounded-circle" src="../../../assets/images/profile_av.png" alt="">
                                        <div class="flex-fill ms-3">
                                            <p class="mb-0 text-muted"><span>Chris Fox</span></p>
                                            <small class="text-muted"><EMAIL></small>
                                            <div>
                                                <a href="auth-signin.html" class="card-link">Sign out</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="list-group m-2">
                                    <a href="profile.html" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-user"></i>Profile & account</a>
                                    <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-gear"></i>Settings</a>
                                    <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-tag"></i>Customization</a>
                                    <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-users"></i>Manage team</a>
                                    <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-calendar"></i>My Events</a>
                                    <a href="#" class="list-group-item list-group-item-action border-0"><i class="w30 fa fa-credit-card"></i>My Statements</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </nav>

        <!-- Sub header: menu -->
        <div class="sub-header">
            <nav class="navbar navbar-expand-lg p-0">
                <div class="container-fluid">

                    <!-- menu toggler -->
                    <button class="navbar-toggler p-0 border-0" type="button" data-bs-toggle="collapse" data-bs-target="#mainHeader">
                        <span class="fa fa-bars"></span>
                    </button>

                    <!-- main menu -->
                    <div class="collapse navbar-collapse order-0 py-1 py-md-2 px-lg-5 px-md-4" id="mainHeader">
                        <ul class="navbar-nav me-auto">
                            <li class="nav-item"><a class="nav-link" href="index.html"><i class="fa fa-dashboard me-2"></i><span>Dashboard</span></a></li>
                            <li class="nav-item"><a class="nav-link active" href="orders.html"><i class="fa fa-cart-plus me-2"></i><span>Orders</span></a></li>
                            <li class="nav-item"><a class="nav-link" href="menu.html"><i class="fa fa-list me-2"></i><span>Menu</span></a></li>
                            <li class="nav-item"><a class="nav-link" href="employees.html"><i class="fa fa-users me-2"></i><span>Employees</span></a></li>
                            <li class="nav-item"><a class="nav-link" href="review.html"><i class="fa fa-star me-2"></i><span>Review</span></a></li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle d-none d-xl-block" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fa fa-file-text me-2"></i><span>Plugins</span>
                                </a>
                                <ul class="dropdown-menu rounded-lg shadow border-0 dropdown-animation">
                                    <li><a class="dropdown-item" href="#">Tomato</a></li>
                                    <li><a class="dropdown-item" href="#">Hber Eats</a></li>
                                    <li><a class="dropdown-item" href="#">Add New</a></li>
                                </ul>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle d-none d-xl-block" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fa fa-file-text me-2"></i><span>Docs</span>
                                </a>
                                <ul class="dropdown-menu rounded-lg shadow border-0 dropdown-animation">
                                    <li><a class="dropdown-item" href="stater-page.html">Stater page</a></li>
                                    <li><a class="dropdown-item" href="../../../documentation/index.html">Documentation</a></li>
                                    <li><a class="dropdown-item" href="../changelog.html">Changelog</a></li>
                                </ul>
                            </li>
                        </ul>
                    </div>

                </div>
            </nav>
        </div>
    </div>

    <!-- main body area -->
    <div class="main px-lg-5 px-md-2">
        
        <!-- Body: Header -->
        <div class="body-header border-bottom d-flex py-3">
            <div class="container-fluid">
                <div class="row align-items-center">
                    <div class="col">
                        <small class="text-muted">Welcome back</small>
                        <h1 class="h4 mt-1">Order List</h1>
                    </div>
                    <div class="col-auto">
                        <a href="https://themeforest.net/user/wrraptheme" title="Download" target="_blank" class="btn btn-white border lift">Download</a>
                        <button type="button" class="btn btn-dark lift">Generate Report</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Body: Body -->
        <div class="body d-flex py-lg-4 py-3">
            <div class="container-fluid px-0">

                <div class="row g-0">
                    <div class="col-12 d-flex food-order">
                        <div class="card bg-transparent border-0 order-0 w-100 list">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-xl-2 col-lg-3 col-md-6 col-sm-6">
                                        <label class="form-label">Select Month</label>
                                        <select class="form-control">
                                            <option>Select</option>
                                            <option>January</option>
                                            <option>February</option>
                                            <option>March</option>
                                            <option>April</option>
                                            <option>May</option>
                                            <option>June</option>
                                            <option>July</option>
                                            <option>August</option>
                                            <option>September</option>
                                            <option>October</option>
                                            <option>November</option>
                                            <option>December</option>
                                        </select>
                                    </div>
                                    <div class="col-xl-4 col-lg-4 col-md-6 col-sm-6">
                                        <label class="form-label">Search...</label>
                                        <input type="text" class="form-control" placeholder="Search...">
                                    </div>
                                    <div class="col-xl-6 col-lg-5 col-md-12 text-lg-end">
                                        <label class="form-label">Action</label>
                                        <div>
                                            <button type="button" class="btn btn-sm btn-primary d-inline-block d-sm-none btn-detail"><i class="fa fa-area-chart"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary"><i class="fa fa-envelope"></i></button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary"><i class="fa fa-download"></i></button>
                                            <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#add-workout">Create New</button>
                                        </div>
                                    </div>
                                </div> <!-- Row end  -->

                                <table class="table table-hover myDataTable table-bordered align-middle mb-0 custom-table">
                                    <thead>
                                        <tr>                                       
                                            <th>Order ID:</th>
                                            <th>Customers</th>
                                            <th>Address</th>
                                            <th>Menu</th>
                                            <th>Status</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>LA-0211</td>
                                            <td>
                                                <a href="javascript:void(0);" class="d-flex link-secondary">
                                                    <img class="avatar sm rounded" src="../../../assets/images/xs/avatar1.jpg" alt="">
                                                    <div class="h6 mb-0 ms-2">Maryam Amiri</div>
                                                </a>
                                            </td>
                                            <td>
                                                <div>44 Shirley Ave. West Chicago, IL 60185</div>
                                                <span class="text-muted">3km</span>
                                            </td>
                                            <td>
                                                <div>Fresh salad bowl (2)</div>
                                                <span class="text-muted">$80.5</span>
                                            </td>
                                            <td><span class="badge bg-success">Completed</span></td>
                                            <td>
                                                <button type="button" class="btn btn-link btn-sm text-muted" data-bs-toggle="tooltip" data-bs-placement="top" title="Send Reports"><i class="fa fa-envelope"></i></button>
                                                <button type="button" class="btn btn-link btn-sm text-muted" data-bs-toggle="tooltip" data-bs-placement="top" title="Download"><i class="fa fa-download"></i></button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>LA-0212</td>
                                            <td>
                                                <a href="javascript:void(0);" class="d-flex link-secondary">
                                                    <img class="avatar sm rounded" src="../../../assets/images/xs/avatar2.jpg" alt="">
                                                    <div class="h6 mb-0 ms-2">jonathan hope</div>
                                                </a>
                                            </td>
                                            <td>
                                                <div>123 6th St. Melbourne, FL 32904</div>
                                                <span class="text-muted">4km</span>
                                            </td>
                                            <td>
                                                <div>Fresh salad bowl (2)</div>
                                                <span class="text-muted">$80.5</span>
                                            </td>
                                            <td><span class="badge bg-success">Completed</span></td>
                                            <td>
                                                <button type="button" class="btn btn-link btn-sm text-muted" data-bs-toggle="tooltip" data-bs-placement="top" title="Send Reports"><i class="fa fa-envelope"></i></button>
                                                <button type="button" class="btn btn-link btn-sm text-muted" data-bs-toggle="tooltip" data-bs-placement="top" title="Download"><i class="fa fa-download"></i></button>
                                            </td>
                                        </tr>
                                        <tr class="table-secondary">
                                            <td>LA-0213</td>
                                            <td>
                                                <a href="javascript:void(0);" class="d-flex link-secondary">
                                                    <img class="avatar sm rounded" src="../../../assets/images/xs/avatar3.jpg" alt="">
                                                    <div class="h6 mb-0 ms-2">Francisco Vasquez</div>
                                                </a>
                                            </td>
                                            <td>
                                                <div>44 Shirley Ave. West Chicago, IL 60185</div>
                                                <span class="text-muted">3km</span>
                                            </td>
                                            <td>
                                                <div>Fresh salad bowl (2)</div>
                                                <span class="text-muted">$80.5</span>
                                            </td>
                                            <td><span class="badge bg-success">Completed</span></td>
                                            <td>
                                                <button type="button" class="btn btn-link btn-sm text-muted" data-bs-toggle="tooltip" data-bs-placement="top" title="Send Reports"><i class="fa fa-envelope"></i></button>
                                                <button type="button" class="btn btn-link btn-sm text-muted" data-bs-toggle="tooltip" data-bs-placement="top" title="Download"><i class="fa fa-download"></i></button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>LA-0215</td>
                                            <td>
                                                <a href="javascript:void(0);" class="d-flex link-secondary">
                                                    <img class="avatar sm rounded" src="../../../assets/images/xs/avatar4.jpg" alt="">
                                                    <div class="h6 mb-0 ms-2">Maryam Amiri</div>
                                                </a>
                                            </td>
                                            <td>
                                                <div>514 S. Magnolia St. Orlando, FL 32806</div>
                                                <span class="text-muted">2.7km</span>
                                            </td>
                                            <td>
                                                <div>Fresh salad bowl (2)</div>
                                                <span class="text-muted">$80.5</span>
                                            </td>
                                            <td><span class="badge bg-info">Delivering</span></td>
                                            <td>
                                                <button type="button" class="btn btn-link btn-sm text-muted" data-bs-toggle="tooltip" data-bs-placement="top" title="Send Reports"><i class="fa fa-envelope"></i></button>
                                                <button type="button" class="btn btn-link btn-sm text-muted" data-bs-toggle="tooltip" data-bs-placement="top" title="Download"><i class="fa fa-download"></i></button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>LA-0217</td>
                                            <td>
                                                <a href="javascript:void(0);" class="d-flex link-secondary">
                                                    <img class="avatar sm rounded" src="../../../assets/images/xs/avatar5.jpg" alt="">
                                                    <div class="h6 mb-0 ms-2">Debra Stewart</div>
                                                </a>
                                            </td>
                                            <td>
                                                <div>44 Shirley Ave. West Chicago, IL 60185</div>
                                                <span class="text-muted">3km</span>
                                            </td>
                                            <td>
                                                <div>Cheese Garlic Bread (10)</div>
                                                <span class="text-muted">$257</span>
                                            </td>
                                            <td><span class="badge bg-warning">Preparing</span></td>
                                            <td>
                                                <button type="button" class="btn btn-link btn-sm text-muted" data-bs-toggle="tooltip" data-bs-placement="top" title="Send Reports"><i class="fa fa-envelope"></i></button>
                                                <button type="button" class="btn btn-link btn-sm text-muted" data-bs-toggle="tooltip" data-bs-placement="top" title="Download"><i class="fa fa-download"></i></button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>LA-0220</td>
                                            <td>
                                                <a href="javascript:void(0);" class="d-flex link-secondary">
                                                    <img class="avatar sm rounded" src="../../../assets/images/xs/avatar6.jpg" alt="">
                                                    <div class="h6 mb-0 ms-2">Jane Hunt</div>
                                                </a>
                                            </td>
                                            <td>
                                                <div>25 6th St. Melbourne, FL 32904</div>
                                                <span class="text-muted">6km</span>
                                            </td>
                                            <td>
                                                <div>Fresh salad bowl (2)</div>
                                                <span class="text-muted">$80.5</span>
                                            </td>
                                            <td><span class="badge bg-danger">Cencal</span></td>
                                            <td>
                                                <button type="button" class="btn btn-link btn-sm text-muted" data-bs-toggle="tooltip" data-bs-placement="top" title="Send Reports"><i class="fa fa-envelope"></i></button>
                                                <button type="button" class="btn btn-link btn-sm text-muted" data-bs-toggle="tooltip" data-bs-placement="top" title="Download"><i class="fa fa-download"></i></button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>LA-0212</td>
                                            <td>
                                                <a href="javascript:void(0);" class="d-flex link-secondary">
                                                    <img class="avatar sm rounded" src="../../../assets/images/xs/avatar7.jpg" alt="">
                                                    <div class="h6 mb-0 ms-2">Francisco Vasquez</div>
                                                </a>
                                            </td>
                                            <td>
                                                <div>23 Shirley Ave. West Chicago, IL 60185</div>
                                                <span class="text-muted">3km</span>
                                            </td>
                                            <td>
                                                <div>Crispy Calamari (3)</div>
                                                <span class="text-muted">$180.5</span>
                                            </td>
                                            <td><span class="badge bg-warning">Preparing</span></td>
                                            <td>
                                                <button type="button" class="btn btn-link btn-sm text-muted" data-bs-toggle="tooltip" data-bs-placement="top" title="Send Reports"><i class="fa fa-envelope"></i></button>
                                                <button type="button" class="btn btn-link btn-sm text-muted" data-bs-toggle="tooltip" data-bs-placement="top" title="Download"><i class="fa fa-download"></i></button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>LA-0213</td>
                                            <td>
                                                <a href="javascript:void(0);" class="d-flex link-secondary">
                                                    <img class="avatar sm rounded" src="../../../assets/images/xs/avatar8.jpg" alt="">
                                                    <div class="h6 mb-0 ms-2">Susie Willis</div>
                                                </a>
                                            </td>
                                            <td>
                                                <div>73 Bowman St. South Windsor, CT 06074</div>
                                                <span class="text-muted">3km</span>
                                            </td>
                                            <td>
                                                <div>Fresh salad bowl (2)</div>
                                                <span class="text-muted">$80.5</span>
                                            </td>
                                            <td><span class="badge bg-warning">Preparing</span></td>
                                            <td>
                                                <button type="button" class="btn btn-link btn-sm text-muted" data-bs-toggle="tooltip" data-bs-placement="top" title="Send Reports"><i class="fa fa-envelope"></i></button>
                                                <button type="button" class="btn btn-link btn-sm text-muted" data-bs-toggle="tooltip" data-bs-placement="top" title="Download"><i class="fa fa-download"></i></button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>LA-0215</td>
                                            <td>
                                                <a href="javascript:void(0);" class="d-flex link-secondary">
                                                    <img class="avatar sm rounded" src="../../../assets/images/xs/avatar9.jpg" alt="">
                                                    <div class="h6 mb-0 ms-2">Debra Stewart</div>
                                                </a>
                                            </td>
                                            <td>
                                                <div>11 Shirley Ave. West Chicago, IL 60185</div>
                                                <span class="text-muted">2.9km</span>
                                            </td>
                                            <td>
                                                <div>Fresh salad bowl (2)</div>
                                                <span class="text-muted">$80.5</span>
                                            </td>
                                            <td><span class="badge bg-info">Delivering</span></td>
                                            <td>
                                                <button type="button" class="btn btn-link btn-sm text-muted" data-bs-toggle="tooltip" data-bs-placement="top" title="Send Reports"><i class="fa fa-envelope"></i></button>
                                                <button type="button" class="btn btn-link btn-sm text-muted" data-bs-toggle="tooltip" data-bs-placement="top" title="Download"><i class="fa fa-download"></i></button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="card border-0 order-1 details">
                            <div class="card-body p-lg-4">
                                <ul class="nav nav-tabs tab-body-header rounded d-flex mt-2 text-center" role="tablist">
                                    <li class="nav-item flex-fill"><a class="nav-link active" data-bs-toggle="tab" href="#nav-Items" role="tab">Items</a></li>
                                    <li class="nav-item flex-fill"><a class="nav-link" data-bs-toggle="tab" href="#nav-Progress" role="tab">Progress</a></li>
                                    <li class="nav-item flex-fill"><a class="nav-link" data-bs-toggle="tab" href="#nav-Reviews" role="tab">Reviews</a></li>
                                </ul>
                                
                                <div class="tab-content mt-2">
                                    <div class="tab-pane fade show active" id="nav-Items" role="tabpanel">
                                        <ul class="list-unstyled list-group list-group-custom list-group-flush mb-0">
                                            <li class="list-group-item py-3 px-0">
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar lg no-thumbnail bg-transparent">
                                                        <img class="img-fluid rounded" src="../../../assets/images/fitness/food-1.jpg" alt="" />    
                                                    </div>
                                                    <div class="flex-fill ms-3 text-truncate">
                                                        <h6 class="mb-0">Fresh salad bowl (2)</h6>
                                                        <span class="text-muted">Extra Cheese</span>
                                                    </div>
                                                </div>
                                            </li>
                                            <li class="list-group-item py-3 px-0">
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar lg no-thumbnail bg-transparent">
                                                        <img class="img-fluid rounded" src="../../../assets/images/fitness/food-2.jpg" alt="" />    
                                                    </div>
                                                    <div class="flex-fill ms-3 text-truncate">
                                                        <h6 class="mb-0">Cheese Garlic Bread (10)</h6>
                                                        <span class="text-muted">Extra Cheese</span>
                                                    </div>
                                                </div>
                                            </li>
                                            <li class="list-group-item py-3 px-0">
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar lg no-thumbnail bg-transparent">
                                                        <img class="img-fluid rounded" src="../../../assets/images/fitness/food-3.jpg" alt="" />    
                                                    </div>
                                                    <div class="flex-fill ms-3 text-truncate">
                                                        <h6 class="mb-0">Fresh salad bowl (3)</h6>
                                                        <span class="text-muted">Extra Cheese</span>
                                                    </div>
                                                </div>
                                            </li>
                                            <li class="list-group-item py-3 px-0">
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar lg no-thumbnail bg-transparent">
                                                        <img class="img-fluid rounded" src="../../../assets/images/fitness/food-4.jpg" alt="" />    
                                                    </div>
                                                    <div class="flex-fill ms-3 text-truncate">
                                                        <h6 class="mb-0">Fresh salad bowl (2)</h6>
                                                        <span class="text-muted">Extra Cheese</span>
                                                    </div>
                                                </div>
                                            </li>
                                            <li class="list-group-item py-3 px-0">
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar lg no-thumbnail bg-transparent">
                                                        <img class="img-fluid rounded" src="../../../assets/images/fitness/food-1.jpg" alt="" />    
                                                    </div>
                                                    <div class="flex-fill ms-3 text-truncate">
                                                        <h6 class="mb-0">Fresh salad bowl (2)</h6>
                                                        <span class="text-muted">Extra Cheese</span>
                                                    </div>
                                                </div>
                                            </li>
                                        </ul>
                                    </div>
                                    <div class="tab-pane fade" id="nav-Progress" role="tabpanel">
                                        <div class="custom_scroll" style="height: 380px; scroll-behavior: smooth;">
                                            <ul class="timeline-activity list-unstyled mb-0">
                                                <li class="activity px-3 py-2 mb-1" data-date="12:30 - Sun">
                                                    <div class="fw-bold small d-flex justify-content-between align-items-center">New Order <span class="badge bg-warning">ID: 215</span></div>
                                                    <div>
                                                        <small class="text-muted">1 Burger, 1 Corn Rice curd</small>
                                                    </div>
                                                </li>
                                                <li class="activity px-3 py-2 mb-1" data-date="12:31 - Sun">
                                                    <div class="fw-bold small">Order Received</div>
                                                    <div>
                                                        
                                                    </div>
                                                </li>
                                                <li class="activity px-3 py-2 mb-1" data-date="12:32 - Sun">
                                                    <div class="fw-bold small">Payment Verify</div>
                                                    <div>
                                                        <h5 class="mb-0 text-success">$80.5 - Done</h5>
                                                        <small class="text-muted">NetBanking</small>
                                                    </div>
                                                </li>
                                                <li class="activity px-3 py-2 mb-1" data-date="12:35 - Sun">
                                                    <div class="fw-bold small">Order inprogress</div>
                                                    <div>
                                                        <label class="me-2">Team:</label>
                                                        <a href="#" title="avatar"><img class="avatar xs rounded" src="../../../assets/images/xs/avatar3.jpg" alt="friend"> </a>
                                                        <a href="#" title="avatar"><img class="avatar xs rounded" src="../../../assets/images/xs/avatar1.jpg" alt="friend"> </a>
                                                        <a href="#" title="avatar"><img class="avatar xs rounded" src="../../../assets/images/xs/avatar7.jpg" alt="friend"> </a>
                                                    </div>
                                                </li>
                                                <li class="activity px-3 py-2 mb-1" data-date="12:55 - Sun">
                                                    <div class="fw-bold small">Delivery on the way</div>
                                                    <div>
                                                        <p class="mb-1 small text-muted"><i class="fa fa-map-marker ps-1"></i> 123 6th St. Melbourne, FL 32904</p>
                                                        <a href="#" title="avatar"><img class="avatar xs rounded" src="../../../assets/images/xs/avatar5.jpg" alt="friend"> </a>
                                                        <label class="ms-1">Robert Hammer</label>
                                                    </div>
                                                </li>
                                                <li class="activity px-3 py-2 mb-1" data-date="01:10 - Sun">
                                                    <div class="fw-bold small d-flex justify-content-between align-items-center">Delivery<span class="badge bg-success">Done</span></div>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade" id="nav-Reviews" role="tabpanel">
                                        ....
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>

        <!-- Body: Footer -->
        <div class="body-footer d-flex">
        </div>

    </div>

    <!-- Modal: Layout -->
    <div class="modal fade" id="LayoutModal" tabindex="-1">
        <div class="modal-dialog modal-lg modal-dialog-vertical modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                        <h5 class="modal-title">Ready to build Layouts</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body custom_scroll">
                    <div class="mb-4">Customize your overview page layout. Choose the one that best fits your needs.</div>
                    <h5 class="mt-5 pb-2">Left sidebar with icon</h5>
                    <div class="row g-3">
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-default.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Default</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../index-mini-sidebar.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-mini-sidebar.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Default + Menu Collapse</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-c/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-c.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Menu + Tab view</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-g/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-g.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Icon menu with Grid view</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-i/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-i.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Dual tone icon + menu list</h6>
                                </div>
                            </a>
                        </div>
                    </div>
                    <h5 class="mt-5 pb-2">Header top menu</h5>
                    <div class="row g-3">
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-d/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-d.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header <span class="text-muted small">(Fluid)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-d-container/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-d-container.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header <span class="text-muted small">(Container)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift border border-primary bg-primary text-light" href="index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-d-sub-header.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Sub menu <span class="text-muted small">(Fluid)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-d-sub-header-container/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-d-sub-header-container.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Submenu <span class="text-muted small">(Container)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-f/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-f.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Submenu, Overlay <span class="text-muted small">(Fluid)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-f-container/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-f-container.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Submenu, Overlay <span class="text-muted small">(Container)</span></h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-l/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-l.svg" alt="">
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Megamenu + Animation Overlay</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-q/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-q.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Megamenu sticky</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-o/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-o.svg" alt="">
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Header + Full Menu sticky</h6>
                                </div>
                            </a>
                        </div>
                    </div>
                    <h5 class="mt-5 pb-2">Content Combinations</h5>
                    <div class="row g-3">
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-b/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-b.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Default</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-e/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-e.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Default</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-h/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-h.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Default</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-k/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-k.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Body Overlay</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-p/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-p.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Background BG</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-n/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-n.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0">Sidebar with Tab</h6>
                                </div>
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <a class="card lift" href="../layout-m/index.html">
                                <img class="card-img-top" src="../../../assets/images/layout/layout-m.svg" alt="" />
                                <div class="card-body text-center">
                                    <h6 class="card-title mb-0 text-white">Sidebar with Header <span class="small">(Fixed)</span></h6>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal: Setting -->
    <div class="modal fade" id="SettingsModal" tabindex="-1">
        <div class="modal-dialog modal-sm modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">AL-UI Setting</h5>
                </div>
                <div class="modal-body custom_scroll">
                    <!-- Settings: Font -->
                    <div class="setting-font">
                        <small class="card-title text-muted">Google font Settings</small>
                        <ul class="list-group font_setting mb-3 mt-1">
                            <li class="list-group-item py-1 px-2">
                                <div class="form-check mb-0">
                                    <input class="form-check-input" type="radio" name="font" id="font-opensans" value="font-opensans" checked="">
                                    <label class="form-check-label" for="font-opensans">
                                        Open Sans Google Font
                                    </label>
                                </div>
                            </li>
                            <li class="list-group-item py-1 px-2">
                                <div class="form-check mb-0">
                                    <input class="form-check-input" type="radio" name="font" id="font-quicksand" value="font-quicksand">
                                    <label class="form-check-label" for="font-quicksand">
                                        Quicksand Google Font
                                    </label>
                                </div>
                            </li>
                            <li class="list-group-item py-1 px-2">
                                <div class="form-check mb-0">
                                    <input class="form-check-input" type="radio" name="font" id="font-nunito" value="font-nunito">
                                    <label class="form-check-label" for="font-nunito">
                                        Nunito Google Font
                                    </label>
                                </div>
                            </li>
                            <li class="list-group-item py-1 px-2">
                                <div class="form-check mb-0">
                                    <input class="form-check-input" type="radio" name="font" id="font-Raleway" value="font-raleway">
                                    <label class="form-check-label" for="font-Raleway">
                                        Raleway Google Font
                                    </label>
                                </div>
                            </li>
                        </ul>
                    </div>
                    <!-- Settings: Color -->
                    <div class="setting-theme">
                        <small class="card-title text-muted">Theme Color Settings</small>
                        <ul class="list-unstyled d-flex justify-content-between choose-skin mb-2 mt-1">
                            <li data-theme="indigo" class="active"><div class="indigo"></div></li>
                            <li data-theme="blue"><div class="blue"></div></li>
                            <li data-theme="cyan"><div class="cyan"></div></li>
                            <li data-theme="green"><div class="green"></div></li>
                            <li data-theme="orange"><div class="orange"></div></li>
                            <li data-theme="blush"><div class="blush"></div></li>
                            <li data-theme="red"><div class="red"></div></li>
                            <li data-theme="dynamic"><div class="dynamic"><i class="fa fa-paint-brush"></i></div></li>
                        </ul>
                        <div class="form-check form-switch gradient-switch mb-1">
                                <input class="form-check-input" type="checkbox" id="CheckGradient">
                                <label class="form-check-label" for="CheckGradient">Enable Gradient! ( Sidebar )</label>
                            </div>
                    </div>
                    <!-- Settings: bg image -->
                    <div class="setting-img mb-3">
                        <div class="form-check form-switch imagebg-switch mb-1">
                            <input class="form-check-input" type="checkbox" id="CheckImage">
                            <label class="form-check-label" for="CheckImage">Set Background Image (Sidebar)</label>
                        </div>
                        <div class="bg-images">
                            <ul class="list-unstyled d-flex justify-content-between">
                                <li class="sidebar-img-1 sidebar-img-active"><a class="rounded sidebar-img" id="img-1" href="#"><img src="../../assets/images/sidebar-bg/sidebar-1.jpg" alt="" /></a></li>
                                <li class="sidebar-img-2"><a class="rounded sidebar-img" id="img-2" href="#"><img src="../../assets/images/sidebar-bg/sidebar-2.jpg" alt="" /></a></li>
                                <li class="sidebar-img-3"><a class="rounded sidebar-img" id="img-3" href="#"><img src="../../assets/images/sidebar-bg/sidebar-3.jpg" alt="" /></a></li>
                                <li class="sidebar-img-4"><a class="rounded sidebar-img" id="img-4" href="#"><img src="../../assets/images/sidebar-bg/sidebar-4.jpg" alt="" /></a></li>
                                <li class="sidebar-img-5"><a class="rounded sidebar-img" id="img-5" href="#"><img src="../../assets/images/sidebar-bg/sidebar-5.jpg" alt="" /></a></li>
                            </ul>
                        </div>
                    </div>
                    <!-- Settings: Theme dynamics -->
                    <div class="dt-setting">
                        <small class="card-title text-muted">Dynamic Color Settings</small>
                        <ul class="list-group list-unstyled mb-3 mt-1">
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label>Primary Color</label>
                                <button id="primaryColorPicker" class="btn bg-primary avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label>Secondary Color</label>
                                <button id="secondaryColorPicker" class="btn bg-secondary avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label class="text-muted small">Chart Color 1</label>
                                <button id="chartColorPicker1" class="btn chart-color1 avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label class="text-muted small">Chart Color 2</label>
                                <button id="chartColorPicker2" class="btn chart-color2 avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label class="text-muted small">Chart Color 3</label>
                                <button id="chartColorPicker3" class="btn chart-color3 avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label class="text-muted small">Chart Color 4</label>
                                <button id="chartColorPicker4" class="btn chart-color4 avatar xs border-0 rounded-0"></button>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center py-1 px-2">
                                <label class="text-muted small">Chart Color 5</label>
                                <button id="chartColorPicker5" class="btn chart-color5 avatar xs border-0 rounded-0"></button>
                            </li>
                        </ul>
                    </div>
                    <!-- Settings: Light/dark -->
                    <div class="setting-mode">
                        <small class="card-title text-muted">Light/Dark & Contrast Layout</small>
                        <ul class="list-group list-unstyled mb-0 mt-1">
                            <li class="list-group-item d-flex align-items-center py-1 px-2">
                                <div class="form-check form-switch theme-switch mb-0">
                                    <input class="form-check-input" type="checkbox" id="theme-switch">
                                    <label class="form-check-label" for="theme-switch">Enable Dark Mode!</label>
                                </div>
                            </li>
                            <li class="list-group-item d-flex align-items-center py-1 px-2">
                                <div class="form-check form-switch theme-high-contrast mb-0">
                                    <input class="form-check-input" type="checkbox" id="theme-high-contrast">
                                    <label class="form-check-label" for="theme-high-contrast">Enable High Contrast</label>
                                </div>
                            </li>
                            <li class="list-group-item d-flex align-items-center py-1 px-2">
                                <div class="form-check form-switch theme-rtl mb-0">
                                    <input class="form-check-input" type="checkbox" id="theme-rtl">
                                    <label class="form-check-label" for="theme-rtl">Enable RTL Mode!</label>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-start text-center">
                    <button type="button" class="btn flex-fill btn-primary lift">Save Changes</button>
                    <button type="button" class="btn flex-fill btn-white border lift" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

</div>

<!-- Jquery Core Js -->
<script src="../../../assets/bundles/libscripts.bundle.js"></script>

<!-- Plugin Js -->
<script src="../../../assets/bundles/dataTables.bundle.js"></script>

<!-- Jquery Page Js -->
<script src="../../../assets/js/template.js"></script>
<script>

    // project data table
    $('.myDataTable').addClass( 'nowrap' );
    $('.myDataTable') .dataTable({
        responsive: true,
        searching: false,
        paging: false,
        ordering: false,
        info: false,
        columnDefs: [{ targets: [-1, -3], className: 'dt-body-right' }]
    });

    $('.btn-detail').on('click', function () {
        $('.details').toggleClass('open');
    });
</script>
</body>
</html>